.mbc {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background-color: #4CAF50; /* 清新的绿色 */
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(76, 175, 80, 0.2);
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.mbc:hover {
    background-color: #45a049; /* 悬停时颜色变深 */
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(76, 175, 80, 0.3);
}


.mbw {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background-color: #FF5252; /* 鲜艳的红色 */
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(255, 82, 82, 0.2);
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.mbw:hover {
    background-color: #FF1744; /* 悬停时颜色变深 */
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(255, 82, 82, 0.3);
}