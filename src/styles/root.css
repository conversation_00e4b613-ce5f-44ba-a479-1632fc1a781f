/* Dark mode colors. */
:root {
	--sl-color-accent-low: #002837;
	--sl-color-accent: #007498;
	--sl-color-accent-high: #88d4f7;
}
/* Light mode colors. */
:root[data-theme="light"] {
	--sl-color-accent-low: #a9e0fb;
	--sl-color-accent: #00769b;
	--sl-color-accent-high: #00384b;
}

:root,
::backdrop {
	/* Flexoki colour palette. */
	--color-black: #100f0f;
	--color-paper: #fffcf0;
	--color-base-50: #f2f0e5;
	--color-base-100: #e6e4d9;
	--color-base-150: #dad8ce;
	--color-base-200: #cecdc3;
	--color-base-300: #b7b5ac;
	--color-base-400: #9f9d96;
	--color-base-500: #878580;
	--color-base-600: #6f6e69;
	--color-base-700: #575653;
	--color-base-800: #403e3c;
	--color-base-850: #343331;
	--color-base-900: #282726;
	--color-base-950: #1c1b1a;
	--color-red-50: #ffe1d5;
	--color-red-100: #ffcabb;
	--color-red-150: #fdb2a2;
	--color-red-200: #f89a8a;
	--color-red-300: #e8705f;
	--color-red-400: #d14d41;
	--color-red-500: #c03e35;
	--color-red-600: #af3029;
	--color-red-700: #942822;
	--color-red-800: #6c201c;
	--color-red-850: #551b18;
	--color-red-900: #3e1715;
	--color-red-950: #261312;
	--color-orange-50: #ffe7ce;
	--color-orange-100: #fed3af;
	--color-orange-150: #fcc192;
	--color-orange-200: #f9ae77;
	--color-orange-300: #ec8b49;
	--color-orange-400: #da702c;
	--color-orange-500: #cb6120;
	--color-orange-600: #bc5215;
	--color-orange-700: #9d4310;
	--color-orange-800: #71320d;
	--color-orange-850: #59290d;
	--color-orange-900: #40200d;
	--color-orange-950: #27180e;
	--color-yellow-50: #faeec6;
	--color-yellow-100: #f6e2a0;
	--color-yellow-150: #f1d67e;
	--color-yellow-200: #eccb60;
	--color-yellow-300: #dfb431;
	--color-yellow-400: #d0a215;
	--color-yellow-500: #be9207;
	--color-yellow-600: #ad8301;
	--color-yellow-700: #8e6b01;
	--color-yellow-800: #664d01;
	--color-yellow-850: #503d02;
	--color-yellow-900: #3a2d04;
	--color-yellow-950: #241e08;
	--color-green-50: #edeecf;
	--color-green-100: #dde2b2;
	--color-green-150: #cdd597;
	--color-green-200: #bec97e;
	--color-green-300: #a0af54;
	--color-green-400: #879a39;
	--color-green-500: #768d21;
	--color-green-600: #66800b;
	--color-green-700: #536907;
	--color-green-800: #3d4c07;
	--color-green-850: #313d07;
	--color-green-900: #252d09;
	--color-green-950: #1a1e0c;
	--color-cyan-50: #ddf1e4;
	--color-cyan-100: #bfe8d9;
	--color-cyan-150: #a2dece;
	--color-cyan-200: #87d3c3;
	--color-cyan-300: #5abdac;
	--color-cyan-400: #3aa99f;
	--color-cyan-500: #2f968d;
	--color-cyan-600: #24837b;
	--color-cyan-700: #1c6c66;
	--color-cyan-800: #164f4a;
	--color-cyan-850: #143f3c;
	--color-cyan-900: #122f2c;
	--color-cyan-950: #101f1d;
	--color-blue-50: #e1eceb;
	--color-blue-100: #c6dde8;
	--color-blue-150: #abcfe2;
	--color-blue-200: #92bfdb;
	--color-blue-300: #66a0c8;
	--color-blue-400: #4385be;
	--color-blue-500: hsl(210, 57%, 45%);
	--color-blue-600: #205ea6;
	--color-blue-700: #1a4f8c;
	--color-blue-800: #163b66;
	--color-blue-850: #133051;
	--color-blue-900: #12253b;
	--color-blue-950: #101a24;
	--color-purple-50: #f0eaec;
	--color-purple-100: #e2d9e9;
	--color-purple-150: #d3cae6;
	--color-purple-200: #c4b9e0;
	--color-purple-300: #a699d0;
	--color-purple-400: #8b7ec8;
	--color-purple-500: #735eb5;
	--color-purple-600: #5e409d;
	--color-purple-700: #4f3685;
	--color-purple-800: #3c2a62;
	--color-purple-850: #31234e;
	--color-purple-900: #261c39;
	--color-purple-950: #1a1623;
	--color-magenta-50: #fee4e5;
	--color-magenta-100: #fccfda;
	--color-magenta-150: #f9b9cf;
	--color-magenta-200: #f4a4c2;
	--color-magenta-300: #e47da8;
	--color-magenta-400: #ce5d97;
	--color-magenta-500: #b74583;
	--color-magenta-600: #a02f6f;
	--color-magenta-700: #87285e;
	--color-magenta-800: #641f46;
	--color-magenta-850: #4f1b39;
	--color-magenta-900: #39172b;
	--color-magenta-950: #24131d;

	/* Colors (dark mode) */
	--sl-color-white: var(--color-base-200);
	--sl-color-gray-1: var(--color-base-200);
	--sl-color-gray-2: var(--color-base-200);
	--sl-color-gray-3: var(--color-base-500);
	--sl-color-gray-4: var(--color-base-700);
	--sl-color-gray-5: var(--color-base-850);
	--sl-color-gray-6: var(--color-base-950);
	--sl-color-black: var(--color-black);

	--sl-color-orange-low: var(--color-orange-900);
	--sl-color-orange: var(--color-orange-300);
	--sl-color-orange-high: var(--color-orange-150);
	--sl-color-green-low: var(--color-green-900);
	--sl-color-green: var(--color-green-300);
	--sl-color-green-high: var(--color-green-150);
	--sl-color-blue-low: var(--color-blue-900);
	--sl-color-blue: var(--color-blue-300);
	--sl-color-blue-high: var(--color-blue-150);
	--sl-color-purple-low: var(--color-purple-900);
	--sl-color-purple: var(--color-purple-300);
	--sl-color-purple-high: var(--color-purple-150);
	--sl-color-red-low: var(--color-red-900);
	--sl-color-red: var(--color-red-300);
	--sl-color-red-high: var(--color-red-150);

	--sl-color-text: var(--sl-color-gray-2);
	--sl-color-text-accent: var(--sl-color-accent-high);
	--sl-color-text-invert: var(--sl-color-black);
	--sl-color-bg: var(--sl-color-black);
	--sl-color-bg-nav: var(--sl-color-bg);
	--sl-color-bg-sidebar: var(--sl-color-bg);
	--sl-color-bg-inline-code: var(--sl-color-gray-5);
	--sl-color-bg-accent: var(--sl-color-accent-high);
	--sl-color-hairline-light: var(--sl-color-gray-5);
	--sl-color-hairline: var(--sl-color-gray-6);
	--sl-color-hairline-shade: var(--sl-color-bg);

	--sl-color-backdrop-overlay: hsla(0, 3%, 6%, 0.66);

	/* Shadows (dark mode) */
	--sl-flexoki-shadow-direction: 1px;
	--sl-flexoki-shadow-shade: hsla(48, 100%, 97%, 0.1);
	--sl-flexoki-emboss-shadow: inset 0 var(--sl-flexoki-shadow-direction)
		var(--sl-flexoki-shadow-shade);
	--sl-shadow-sm: 0px 1px 1px hsla(0, 3%, 6%, 0.12), 0px 2px 1px hsla(0, 3%, 6%, 0.24);
	--sl-shadow-md: 0px 8px 4px hsla(0, 3%, 6%, 0.08), 0px 5px 2px hsla(0, 3%, 6%, 0.08),
		0px 3px 2px hsla(0, 3%, 6%, 0.12), 0px 1px 1px hsla(0, 3%, 6%, 0.15);
	--sl-shadow-lg: 0px 25px 7px hsla(0, 3%, 6%, 0.03), 0px 16px 6px hsla(0, 3%, 6%, 0.1),
		0px 9px 5px hsla(223, 13%, 10%, 0.33), 0px 4px 4px hsla(0, 3%, 6%, 0.75),
		0px 4px 2px hsla(0, 3%, 6%, 0.25);
}

:root[data-theme='light'],
[data-theme='light'] ::backdrop {
	/* Colours (light mode) */
	--sl-color-white: var(--color-black);
	--sl-color-gray-1: var(--color-black);
	--sl-color-gray-2: var(--color-black);
	--sl-color-gray-3: var(--color-base-600);
	--sl-color-gray-4: var(--color-base-500);
	--sl-color-gray-5: var(--color-base-200);
	--sl-color-gray-6: var(--color-base-100);
	--sl-color-gray-7: var(--color-base-50);
	--sl-color-black: var(--color-paper);

	--sl-color-orange-high: var(--color-orange-700);
	--sl-color-orange: var(--color-orange-700);
	--sl-color-orange-low: var(--color-orange-50);
	--sl-color-green-high: var(--color-green-700);
	--sl-color-green: var(--color-green-700);
	--sl-color-green-low: var(--color-green-50);
	--sl-color-blue-high: var(--color-blue-700);
	--sl-color-blue: var(--color-blue-700);
	--sl-color-blue-low: var(--color-blue-50);
	--sl-color-purple-high: var(--color-purple-700);
	--sl-color-purple: var(--color-purple-700);
	--sl-color-purple-low: var(--color-purple-50);
	--sl-color-red-high: var(--color-red-700);
	--sl-color-red: var(--color-red-700);
	--sl-color-red-low: var(--color-red-50);

	--sl-color-text-accent: var(--sl-color-accent);
	--sl-color-text-invert: var(--sl-color-black);
	--sl-color-bg-nav: var(--sl-color-bg);
	--sl-color-bg-sidebar: var(--sl-color-bg);
	--sl-color-bg-inline-code: var(--sl-color-gray-6);
	--sl-color-bg-accent: var(--sl-color-accent);
	--sl-color-hairline-light: var(--sl-color-gray-6);
	--sl-color-hairline-shade: var(--sl-color-gray-6);

	--sl-color-backdrop-overlay: hsla(0, 3%, 6%, 0.33);

	/* Shadows (light mode) */
	--sl-flexoki-shadow-direction: -1px;
	--sl-flexoki-shadow-shade: hsla(0, 3%, 6%, 0.1);
	--sl-shadow-sm: 0px 1px 1px hsla(0, 3%, 6%, 0.06), 0px 2px 1px hsla(0, 3%, 6%, 0.06);
	--sl-shadow-md: 0px 8px 4px hsla(0, 3%, 6%, 0.03), 0px 5px 2px hsla(0, 3%, 6%, 0.03),
		0px 3px 2px hsla(0, 3%, 6%, 0.06), 0px 1px 1px hsla(0, 3%, 6%, 0.06);
	--sl-shadow-lg: 0px 25px 7px hsla(0, 3%, 6%, 0.01), 0px 16px 6px hsla(0, 3%, 6%, 0.03),
		0px 9px 5px hsla(223, 13%, 10%, 0.08), 0px 4px 4px hsla(0, 3%, 6%, 0.16),
		0px 4px 2px hsla(0, 3%, 6%, 0.04);
}