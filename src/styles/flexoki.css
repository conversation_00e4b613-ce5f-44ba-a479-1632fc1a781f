:root,
::backdrop {
	/* Flexoki colour palette. */
	--color-black: #100f0f;
	--color-paper: #fffcf0;
	--color-base-50: #f2f0e5;
	--color-base-100: #e6e4d9;
	--color-base-150: #dad8ce;
	--color-base-200: #cecdc3;
	--color-base-300: #b7b5ac;
	--color-base-400: #9f9d96;
	--color-base-500: #878580;
	--color-base-600: #6f6e69;
	--color-base-700: #575653;
	--color-base-800: #403e3c;
	--color-base-850: #343331;
	--color-base-900: #282726;
	--color-base-950: #1c1b1a;
	--color-red-50: #ffe1d5;
	--color-red-100: #ffcabb;
	--color-red-150: #fdb2a2;
	--color-red-200: #f89a8a;
	--color-red-300: #e8705f;
	--color-red-400: #d14d41;
	--color-red-500: #c03e35;
	--color-red-600: #af3029;
	--color-red-700: #942822;
	--color-red-800: #6c201c;
	--color-red-850: #551b18;
	--color-red-900: #3e1715;
	--color-red-950: #261312;
	--color-orange-50: #ffe7ce;
	--color-orange-100: #fed3af;
	--color-orange-150: #fcc192;
	--color-orange-200: #f9ae77;
	--color-orange-300: #ec8b49;
	--color-orange-400: #da702c;
	--color-orange-500: #cb6120;
	--color-orange-600: #bc5215;
	--color-orange-700: #9d4310;
	--color-orange-800: #71320d;
	--color-orange-850: #59290d;
	--color-orange-900: #40200d;
	--color-orange-950: #27180e;
	--color-yellow-50: #faeec6;
	--color-yellow-100: #f6e2a0;
	--color-yellow-150: #f1d67e;
	--color-yellow-200: #eccb60;
	--color-yellow-300: #dfb431;
	--color-yellow-400: #d0a215;
	--color-yellow-500: #be9207;
	--color-yellow-600: #ad8301;
	--color-yellow-700: #8e6b01;
	--color-yellow-800: #664d01;
	--color-yellow-850: #503d02;
	--color-yellow-900: #3a2d04;
	--color-yellow-950: #241e08;
	--color-green-50: #edeecf;
	--color-green-100: #dde2b2;
	--color-green-150: #cdd597;
	--color-green-200: #bec97e;
	--color-green-300: #a0af54;
	--color-green-400: #879a39;
	--color-green-500: #768d21;
	--color-green-600: #66800b;
	--color-green-700: #536907;
	--color-green-800: #3d4c07;
	--color-green-850: #313d07;
	--color-green-900: #252d09;
	--color-green-950: #1a1e0c;
	--color-cyan-50: #ddf1e4;
	--color-cyan-100: #bfe8d9;
	--color-cyan-150: #a2dece;
	--color-cyan-200: #87d3c3;
	--color-cyan-300: #5abdac;
	--color-cyan-400: #3aa99f;
	--color-cyan-500: #2f968d;
	--color-cyan-600: #24837b;
	--color-cyan-700: #1c6c66;
	--color-cyan-800: #164f4a;
	--color-cyan-850: #143f3c;
	--color-cyan-900: #122f2c;
	--color-cyan-950: #101f1d;
	--color-blue-50: #e1eceb;
	--color-blue-100: #c6dde8;
	--color-blue-150: #abcfe2;
	--color-blue-200: #92bfdb;
	--color-blue-300: #66a0c8;
	--color-blue-400: #4385be;
	--color-blue-500: hsl(210, 57%, 45%);
	--color-blue-600: #205ea6;
	--color-blue-700: #1a4f8c;
	--color-blue-800: #163b66;
	--color-blue-850: #133051;
	--color-blue-900: #12253b;
	--color-blue-950: #101a24;
	--color-purple-50: #f0eaec;
	--color-purple-100: #e2d9e9;
	--color-purple-150: #d3cae6;
	--color-purple-200: #c4b9e0;
	--color-purple-300: #a699d0;
	--color-purple-400: #8b7ec8;
	--color-purple-500: #735eb5;
	--color-purple-600: #5e409d;
	--color-purple-700: #4f3685;
	--color-purple-800: #3c2a62;
	--color-purple-850: #31234e;
	--color-purple-900: #261c39;
	--color-purple-950: #1a1623;
	--color-magenta-50: #fee4e5;
	--color-magenta-100: #fccfda;
	--color-magenta-150: #f9b9cf;
	--color-magenta-200: #f4a4c2;
	--color-magenta-300: #e47da8;
	--color-magenta-400: #ce5d97;
	--color-magenta-500: #b74583;
	--color-magenta-600: #a02f6f;
	--color-magenta-700: #87285e;
	--color-magenta-800: #641f46;
	--color-magenta-850: #4f1b39;
	--color-magenta-900: #39172b;
	--color-magenta-950: #24131d;

	/* Colors (dark mode) */
	--sl-color-white: var(--color-base-200);
	--sl-color-gray-1: var(--color-base-200);
	--sl-color-gray-2: var(--color-base-200);
	--sl-color-gray-3: var(--color-base-500);
	--sl-color-gray-4: var(--color-base-700);
	--sl-color-gray-5: var(--color-base-850);
	--sl-color-gray-6: var(--color-base-950);
	--sl-color-black: var(--color-black);

	--sl-color-orange-low: var(--color-orange-900);
	--sl-color-orange: var(--color-orange-300);
	--sl-color-orange-high: var(--color-orange-150);
	--sl-color-green-low: var(--color-green-900);
	--sl-color-green: var(--color-green-300);
	--sl-color-green-high: var(--color-green-150);
	--sl-color-blue-low: var(--color-blue-900);
	--sl-color-blue: var(--color-blue-300);
	--sl-color-blue-high: var(--color-blue-150);
	--sl-color-purple-low: var(--color-purple-900);
	--sl-color-purple: var(--color-purple-300);
	--sl-color-purple-high: var(--color-purple-150);
	--sl-color-red-low: var(--color-red-900);
	--sl-color-red: var(--color-red-300);
	--sl-color-red-high: var(--color-red-150);

	--sl-color-text: var(--sl-color-gray-2);
	--sl-color-text-accent: var(--sl-color-accent-high);
	--sl-color-text-invert: var(--sl-color-black);
	--sl-color-bg: var(--sl-color-black);
	--sl-color-bg-nav: var(--sl-color-bg);
	--sl-color-bg-sidebar: var(--sl-color-bg);
	--sl-color-bg-inline-code: var(--sl-color-gray-5);
	--sl-color-bg-accent: var(--sl-color-accent-high);
	--sl-color-hairline-light: var(--sl-color-gray-5);
	--sl-color-hairline: var(--sl-color-gray-6);
	--sl-color-hairline-shade: var(--sl-color-bg);

	--sl-color-backdrop-overlay: hsla(0, 3%, 6%, 0.66);

	/* Shadows (dark mode) */
	--sl-flexoki-shadow-direction: 1px;
	--sl-flexoki-shadow-shade: hsla(48, 100%, 97%, 0.1);
	--sl-flexoki-emboss-shadow: inset 0 var(--sl-flexoki-shadow-direction)
		var(--sl-flexoki-shadow-shade);
	--sl-shadow-sm: 0px 1px 1px hsla(0, 3%, 6%, 0.12), 0px 2px 1px hsla(0, 3%, 6%, 0.24);
	--sl-shadow-md: 0px 8px 4px hsla(0, 3%, 6%, 0.08), 0px 5px 2px hsla(0, 3%, 6%, 0.08),
		0px 3px 2px hsla(0, 3%, 6%, 0.12), 0px 1px 1px hsla(0, 3%, 6%, 0.15);
	--sl-shadow-lg: 0px 25px 7px hsla(0, 3%, 6%, 0.03), 0px 16px 6px hsla(0, 3%, 6%, 0.1),
		0px 9px 5px hsla(223, 13%, 10%, 0.33), 0px 4px 4px hsla(0, 3%, 6%, 0.75),
		0px 4px 2px hsla(0, 3%, 6%, 0.25);
}

:root[data-theme='light'],
[data-theme='light'] ::backdrop {
	/* Colours (light mode) */
	--sl-color-white: var(--color-black);
	--sl-color-gray-1: var(--color-black);
	--sl-color-gray-2: var(--color-black);
	--sl-color-gray-3: var(--color-base-600);
	--sl-color-gray-4: var(--color-base-500);
	--sl-color-gray-5: var(--color-base-200);
	--sl-color-gray-6: var(--color-base-100);
	--sl-color-gray-7: var(--color-base-50);
	--sl-color-black: var(--color-paper);

	--sl-color-orange-high: var(--color-orange-700);
	--sl-color-orange: var(--color-orange-700);
	--sl-color-orange-low: var(--color-orange-50);
	--sl-color-green-high: var(--color-green-700);
	--sl-color-green: var(--color-green-700);
	--sl-color-green-low: var(--color-green-50);
	--sl-color-blue-high: var(--color-blue-700);
	--sl-color-blue: var(--color-blue-700);
	--sl-color-blue-low: var(--color-blue-50);
	--sl-color-purple-high: var(--color-purple-700);
	--sl-color-purple: var(--color-purple-700);
	--sl-color-purple-low: var(--color-purple-50);
	--sl-color-red-high: var(--color-red-700);
	--sl-color-red: var(--color-red-700);
	--sl-color-red-low: var(--color-red-50);

	--sl-color-text-accent: var(--sl-color-accent);
	--sl-color-text-invert: var(--sl-color-black);
	--sl-color-bg-nav: var(--sl-color-bg);
	--sl-color-bg-sidebar: var(--sl-color-bg);
	--sl-color-bg-inline-code: var(--sl-color-gray-6);
	--sl-color-bg-accent: var(--sl-color-accent);
	--sl-color-hairline-light: var(--sl-color-gray-6);
	--sl-color-hairline-shade: var(--sl-color-gray-6);

	--sl-color-backdrop-overlay: hsla(0, 3%, 6%, 0.33);

	/* Shadows (light mode) */
	--sl-flexoki-shadow-direction: -1px;
	--sl-flexoki-shadow-shade: hsla(0, 3%, 6%, 0.1);
	--sl-shadow-sm: 0px 1px 1px hsla(0, 3%, 6%, 0.06), 0px 2px 1px hsla(0, 3%, 6%, 0.06);
	--sl-shadow-md: 0px 8px 4px hsla(0, 3%, 6%, 0.03), 0px 5px 2px hsla(0, 3%, 6%, 0.03),
		0px 3px 2px hsla(0, 3%, 6%, 0.06), 0px 1px 1px hsla(0, 3%, 6%, 0.06);
	--sl-shadow-lg: 0px 25px 7px hsla(0, 3%, 6%, 0.01), 0px 16px 6px hsla(0, 3%, 6%, 0.03),
		0px 9px 5px hsla(223, 13%, 10%, 0.08), 0px 4px 4px hsla(0, 3%, 6%, 0.16),
		0px 4px 2px hsla(0, 3%, 6%, 0.04);
}

/* Nav Bar */
.header {
	border-bottom: 0;
}
starlight-lang-select label,
starlight-theme-select label {
	color: var(--sl-color-gray-3);
}
site-search button[data-open-modal] {
	color: var(--sl-color-gray-3);
	kbd {
		background-color: transparent;
	}
}
starlight-menu-button button,
[data-theme='light'] starlight-menu-button button {
	color: var(--sl-color-text);
	background-color: var(--sl-color-gray-7, var(--sl-color-gray-6));
	box-shadow: var(--sl-flexoki-emboss-shadow);
}
[data-theme='dark'] starlight-menu-button[aria-expanded='true'] button {
	color: var(--sl-color-text-invert);
}

/* Sidebar */
.sidebar-pane {
	border-inline-end: 0;
}
@media (min-width: 50rem) {
	:root {
		--sl-sidebar-pad-x: 1.25rem;
	}
}
.sidebar-content {
	padding-top: 1.5rem;

	summary {
		gap: 0.125rem;
		padding-inline: 0;
		flex-direction: row-reverse;
		justify-content: flex-end;
		font-weight: 600;

		.caret {
			--sl-icon-color: var(--sl-color-gray-3);
		}
		@media (min-width: 50rem) {
			/* Tweak alignment of group label and caret icon */
			--caret-adjustment: 0.5px;
			transform: translateY(var(--caret-adjustment));
			.group-label {
				transform: translateY(calc(-1 * var(--caret-adjustment)));
			}
		}
	}

	li {
		border: 0;
		--sl-sidebar-item-padding-inline: 0.45rem;
	}
	.top-level > li + li {
		margin-block: 0;
	}
	.top-level > li:has(details) {
		margin-block: 0.5rem;

		&:first-child {
			margin-top: 0;
		}

		&:last-child {
			margin-bottom: 0;
		}
	}

	.large {
		font-size: inherit;
		font-weight: inherit;

		@media (min-width: 50rem) {
			font-size: var(--sl-text-sm);
		}
	}

	a {
		color: var(--sl-color-gray-3);
		&:hover {
			color: var(--sl-color-gray-1);
		}
		&[aria-current='page'] {
			background-color: transparent;
			color: var(--sl-color-text-accent);
			font-weight: 600;
		}
	}
}

/* TOC */
.right-sidebar {
	/* Remove border dividing right sidebar from main content column. */
	border-inline-start: 0;
}
.right-sidebar-panel {
	/* Top padding to move TOC down and aligned with page title. */
	padding-top: 1.8125rem;
	/* Spacing between main column and TOC. */
	padding-inline-start: 2rem;

	h2 {
		/* Smaller TOC heading. */
		font-size: var(--sl-text-sm);
	}
	a {
		/* Remove indentation on top-level of TOC links. */
		--pad-inline: 0px;
	}
}
.content-panel {
	/* Less vertical padding around page title. */
	padding-block: 0.5rem;

	& + .content-panel {
		/* Remove border between page title and main content. */
		border-top: 0;
	}
}
mobile-starlight-toc {
	summary {
		border-bottom: 0;
	}
	.toggle {
		border: 0;
		box-shadow: var(--sl-flexoki-emboss-shadow);
		background-color: var(--sl-color-gray-7, var(--sl-color-gray-6));

		[open] & {
			background-color: var(--sl-color-gray-5);
			border-color: var(--sl-color-gray-5);
		}
	}
	.display-current {
		color: var(--sl-color-gray-3);
	}
	.dropdown {
		margin-inline: 1rem;
		margin-bottom: -0.5rem;
		border-top: 0;
		border-inline: 0;
		border-radius: 0.5rem;
		background-color: var(--sl-color-gray-7, var(--sl-color-gray-6));
		box-shadow: var(--sl-flexoki-emboss-shadow), var(--sl-shadow-md);
	}
	.isMobile a {
		--pad-inline: 0.75rem;
		border-top: 0;
		padding-block: 0.4375rem;
		font-size: var(--sl-text-xs);
	}
	.isMobile > :first-child a {
		padding-top: 0.625rem;
	}
	.isMobile > :last-child a {
		padding-bottom: 0.625rem;
	}
}

:root {
	/* Base font size. Theoretical range: 14px–20px. In practice: ~15px–20px. */
	--sl-flexoki-text-base: min(calc(0.875rem + 0.3vw), 1.25rem);
	--sl-flexoki-text-sm: calc(0.875 * var(--sl-flexoki-text-base));
}

.content-panel {
	/* Responsive text sizes for main content. */
	--sl-text-2xs: calc(0.75 * var(--sl-text-base));
	--sl-text-xs: calc(0.8125 * var(--sl-text-base));
	--sl-text-sm: var(--sl-flexoki-text-sm);
	--sl-text-base: var(--sl-flexoki-text-base);
	--sl-text-lg: calc(1.125 * var(--sl-text-base));
	--sl-text-xl: calc(1.25 * var(--sl-text-base));
	--sl-text-2xl: calc(1.5 * var(--sl-text-base));
	--sl-text-3xl: calc(1.8125 * var(--sl-text-base));
	--sl-text-4xl: calc(2.1875 * var(--sl-text-base));
	--sl-text-5xl: calc(2.625 * var(--sl-text-base));
	--sl-text-6xl: calc(4 * var(--sl-text-base));

	--sl-text-body: var(--sl-text-base);
	--sl-text-body-sm: var(--sl-text-xs);
	--sl-text-code: var(--sl-text-sm);
	--sl-text-code-sm: var(--sl-text-xs);
	--sl-text-h1: var(--sl-text-4xl);
	--sl-text-h2: var(--sl-text-3xl);
	--sl-text-h3: var(--sl-text-2xl);
	--sl-text-h4: var(--sl-text-xl);
	--sl-text-h5: var(--sl-text-lg);
}

/* Headings */
h1#_top,
.sl-markdown-content :is(h1, h2, h3, h4, h5, h6):not(:where(.not-content *)) {
	font-weight: 500;
}

.sl-markdown-content {
	font-size: var(--sl-text-body);

	/* Links */
	a:not(:where(.not-content *)) {
		color: var(--sl-color-text);
		text-underline-offset: 0.15em;

		&:hover {
			color: var(--sl-color-text-accent);
		}
	}

	/* Blockquote */
	blockquote:not(:where(.not-content *)) {
		border-inline-start-width: 2px;
	}

	/* Inline code */
	code:not(:where(.not-content *)) {
		border-radius: 0.1875em;
	}

	/* Spacing */
	:not(a, strong, em, del, span, input, code, br)
		+ :not(a, strong, em, del, span, input, code, br, :where(.not-content *)) {
		margin-top: 1em;
	}
	/* Headings after non-headings have more spacing. */
	:not(h1, h2, h3, h4, h5, h6) + :is(h1, h2, h3, h4, h5, h6):not(:where(.not-content *)) {
		margin-top: 1.5em;
	}
	li + li:not(:where(.not-content *)),
	dt + dt:not(:where(.not-content *)),
	dt + dd:not(:where(.not-content *)),
	dd + dd:not(:where(.not-content *)) {
		margin-top: 0.25em;
	}
	li
		> :last-child:not(
			li,
			ul,
			ol,
			a,
			strong,
			em,
			del,
			span,
			input,
			code,
			br,
			:where(.not-content *)
		),
	li > :nth-last-child(2):is(starlight-tabs) {
		margin-bottom: 1.75em;
	}

	/* Asides */
	.starlight-aside {
		border: 0;
		border-radius: 0.25rem;
	}
	.starlight-aside__title {
		font-size: var(--sl-text-base);
		font-weight: 500;
	}
	.starlight-aside__icon {
		font-size: var(--sl-text-lg);
	}

	/* Steps */
	.sl-steps {
		& > li::before {
			box-shadow: none;
			background-color: var(--sl-color-gray-6);
		}
		& > li::after {
			background-color: var(--sl-flexoki-shadow-shade);
		}
	}

	/* Cards */
	.card {
		border: 0;
		border-radius: 0.25rem;
		background-color: var(--sl-color-gray-7, var(--sl-color-gray-6));
		padding: 1.5em;

		.title {
			font-size: var(--sl-text-body);
			gap: 0.5em;
		}

		.icon {
			border: 0;
			padding: 0;
			background-color: transparent;
		}
	}

	/* Link Cards */
	.sl-link-card {
		box-shadow: none;

		.title {
			font-size: var(--sl-text-body);
		}
		.description {
			font-size: var(--sl-text-sm);
		}
	}

	/* File Tree */
	starlight-file-tree {
		border: 0;
		border-radius: 0.25rem;
		background-color: var(--sl-color-gray-7, var(--sl-color-gray-6));
	}
}

/* Badges */
.sl-badge:is(.sl-badge) {
	font-family: var(--__sl-font);
	font-weight: 500;
	line-height: 1;

	&.small {
		padding: 0.2625em 0.425em;
		font-size: var(--sl-text-2xs);
	}
	&.medium {
		padding: 0.2625em 0.425em;
		font-size: var(--sl-text-sm);
	}
	&.large {
		padding: 0.2625em 0.425em;
		font-size: var(--sl-text-body);
	}

	/* Default */
	--sl-badge-default-border: var(--sl-color-accent);
	--sl-badge-default-bg: var(--sl-badge-default-border);
	--sl-badge-default-text: var(--sl-color-accent-low);
	/* Note */
	--sl-badge-note-border: var(--sl-color-blue);
	--sl-badge-note-bg: var(--sl-badge-note-border);
	--sl-badge-note-text: var(--sl-color-blue-low);
	/* Danger */
	--sl-badge-danger-border: var(--sl-color-red);
	--sl-badge-danger-bg: var(--sl-badge-danger-border);
	--sl-badge-danger-text: var(--sl-color-red-low);
	/* Success */
	--sl-badge-success-border: var(--sl-color-green);
	--sl-badge-success-bg: var(--sl-badge-success-border);
	--sl-badge-success-text: var(--sl-color-green-low);
	/* Caution */
	--sl-badge-caution-border: var(--sl-color-orange);
	--sl-badge-caution-bg: var(--sl-badge-caution-border);
	--sl-badge-caution-text: var(--sl-color-orange-low);
	/* Tip */
	--sl-badge-tip-border: var(--sl-color-purple);
	--sl-badge-tip-bg: var(--sl-badge-tip-border);
	--sl-badge-tip-text: var(--sl-color-purple-low);

	[data-theme='light'] & {
		/* Default */
		--sl-badge-default-border: var(--sl-color-accent-low);
		--sl-badge-default-text: var(--sl-color-accent-high);
		/* Note */
		--sl-badge-note-border: var(--sl-color-blue-low);
		--sl-badge-note-text: var(--sl-color-blue-high);
		/* Danger */
		--sl-badge-danger-border: var(--sl-color-red-low);
		--sl-badge-danger-text: var(--sl-color-red-high);
		/* Success */
		--sl-badge-success-border: var(--sl-color-green-low);
		--sl-badge-success-text: var(--sl-color-green-high);
		/* Caution */
		--sl-badge-caution-border: var(--sl-color-orange-low);
		--sl-badge-caution-text: var(--sl-color-orange-high);
		/* Tip */
		--sl-badge-tip-border: var(--sl-color-purple-low);
		--sl-badge-tip-text: var(--sl-color-purple-high);
	}
}

.sidebar-content a[aria-current='page'] > .sl-badge:is(.sl-badge) {
	--sl-color-bg-badge: var(--sl-badge-default-bg);
	--sl-color-border-badge: var(--sl-badge-default-border);
	color: var(--sl-badge-default-text);
	font-weight: 500;

	&.note {
		--sl-color-bg-badge: var(--sl-badge-note-bg);
		--sl-color-border-badge: var(--sl-badge-note-border);
		color: var(--sl-badge-note-text);
	}
	&.danger {
		--sl-color-bg-badge: var(--sl-badge-danger-bg);
		--sl-color-border-badge: var(--sl-badge-danger-border);
		color: var(--sl-badge-danger-text);
	}
	&.success {
		--sl-color-bg-badge: var(--sl-badge-success-bg);
		--sl-color-border-badge: var(--sl-badge-success-border);
		color: var(--sl-badge-success-text);
	}
	&.caution {
		--sl-color-bg-badge: var(--sl-badge-caution-bg);
		--sl-color-border-badge: var(--sl-badge-caution-border);
		color: var(--sl-badge-caution-text);
	}
	&.tip {
		--sl-color-bg-badge: var(--sl-badge-tip-bg);
		--sl-color-border-badge: var(--sl-badge-tip-border);
		color: var(--sl-badge-tip-text);
	}
}

footer {
	.meta {
		font-size: var(--sl-text-xs);
	}
}

/* Search modal */
#starlight__search {
	--sl-search-corners: calc(0.75rem * var(--pagefind-ui-scale));

	.pagefind-ui__result-inner {
		gap: 0;
	}

	.pagefind-ui__result-title,
	.pagefind-ui__result-nested {
		outline-offset: -2px;
		outline-width: 2px !important;
	}
}

/* Banner */
.sl-banner {
	margin: 1rem var(--sl-nav-pad-x);
	box-shadow: none;
	border-radius: 999rem;
	background-color: var(--sl-color-accent-low);

	&,
	a {
		color: var(--sl-color-text);
	}
	a:hover {
		color: var(--sl-color-text-accent);
	}

	@media (min-width: 50rem) {
		margin-top: 2rem;

		[data-has-sidebar] & {
			margin-inline-start: 0;
		}
	}
	@media (min-width: 72rem) {
		[data-has-toc] & {
			margin-inline-end: 0;
		}
	}
}


