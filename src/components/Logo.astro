---
import { logos } from 'virtual:starlight/user-images';
import config from 'virtual:starlight/user-config';
import type { Props } from "@astrojs/starlight/props";
---

	{
		config.logo && logos.dark && (
			<>
				<img
					class:list={{ 'light:sl-hidden': !('src' in config.logo) }}
					alt={config.logo.alt}
					src={logos.dark.src}
					width={logos.dark.width}
					height={logos.dark.height}
				/>
				{/* Show light alternate if a user configure both light and dark logos. */}
				{!('src' in config.logo) && (
					<img
						class="dark:sl-hidden"
						alt={config.logo.alt}
						src={logos.light?.src}
						width={logos.light?.width}
						height={logos.light?.height}
					/>
				)}
			</>
		)
	}
<style>
	img {
		height: calc(var(--sl-nav-height) - 2 * var(--sl-nav-pad-y));
		width: auto;
		max-width: 100%;
		object-fit: contain;
		object-position: 0 50%;
	}
</style>
