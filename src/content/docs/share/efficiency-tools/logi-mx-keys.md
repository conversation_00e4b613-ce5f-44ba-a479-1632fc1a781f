---
title: 罗技MX Keys
description: 罗技MX Keys
template: doc
tableOfContents: false
lastUpdated: 2024-07-21 02:57:39
---
#### 购买理由
1. 因为买了[罗技MX Master](/share/efficiency-tools/logi-mx-master),需要想买罗技[MX Keys](https://union-click.jd.com/jdc?e=618%7Cpc%7C&p=JF8BAQcJK1olXDYCVV9cDEMfCm8OHlklGVlaCgFtUQ5SQi0DBUVNGFJeSwUIFxlJX3EIGloUWQ4KXV5bDUkIWipURmtuHw9iEQ4cbylefS13c1kTJkRAVV0LBEcnAl8LGlsRWgAHVlpVOHsXBF9edVsUXAcDVV9eDUInAl8IHVgVWQcCUlhbDUMTM2gIEmtOCGgGAQ4JABxDBmcMS1hAbTYyV25aCEIDBR1JSU8TLzYyVG5eOEsWA2cKGlkWXg4ASF5dC0kWB3MIHVgVWQcCUl9fCUMeM20JGl8cbTYyKC0oTh8UASYMaCRCBl15HCQGVDITSxpMdVloNmZgFT8Na09ERBhdaTJzOjYHZA)。于是就买了。
2. 质感太棒了,做工扎实。你见到实物我估计你也会喜欢的
3. 安静。键程是我喜欢的;按键中间凹陷可以很好的包覆住指窝
4. 超长续航,和[罗技MX Master](/share/efficiency-tools/logi-mx-master)一样出色
5. 简单的键盘背景灯光,通过设置在需要的时候才会亮起


![](/share/efficiency-tools/MXKeysForMac.webp)

#### 购买的版本
我购买的键盘都是for Mac版本的,因为家里的电脑是苹果的。MX Keys还有兼容苹果和Windows的,但是我没有买,因为我追求的美和工作环境的稳定。而Windows系统在这2点上都不符合我的要求,故没有购买兼容版本的,只买了forMac版本的。

**提示:** forMac版本只是键盘布局是Mac的布局,并不是说不能在Windows上使用,在Windows上是能正常使用的。我就在公司的windows主机上使用forMac的MX Keys,非常好用,只是要把forMac的部分键位在脑海里映射到Windows的布局即可,适应几个小时就OK了。
> 在Windows系统上使用软件来修改键盘映射这个不太推荐,因为改了之后会出现很多误操作,给我的感觉是只修改了几个按键的映射,却把其他软件的快捷键都搞乱了一样。还是建议如果在Windows主机上使用forMac的键盘,直接应用于工作中,适应几个小时就OK了


##### 唯一需要映射的键
> 也只是叫法不一样,其他的都一样,所以只需要适应一下叫法即可

Mac的Command键对应的Windows的Win键,其他的都不变,都按照各自系统的键盘使用来正常使用就行了。


---
:::tip[购入情况]
MX Keys和MX Master,我买了2套,一套放公司用,一套家里用,这样就不用来回带了,大大降低了我的负担,不然我每天上下班背个键鼠很麻烦。而且万一哪天漏带了其中一个或者都漏带了,上班就比较难受了，因为之前针对工作环境设置过的自定义按键功能都没有了,工作效率大大折扣。
:::
