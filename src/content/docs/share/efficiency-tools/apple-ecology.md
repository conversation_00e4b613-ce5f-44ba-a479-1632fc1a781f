---
title: 苹果生态
description: 苹果生态
template: doc
lastUpdated: 2024-07-20 16:27:11
---

:::note[前提]
以下内容是基于[我是软件工程师](/contact)的背景下的写的。其他行业另说
:::

#### 使用统一生态的好处
- 各个产品体验一致性做得很好
- 真正的互联互通,从底层打通
- 平衡,生态中的产品各司其职

#### 不使用统一生态,带来的问题
- 成本高昂。为了适应不同场景下的功能,需要付出很多努力。例如针对某一功能,下载不同平台对应的软件,这些软件提供的功能很大可能是有部分缺失的(受平台限制无法开发对应功能)
- 通信成本高。因为未从底层打通
- 严重失衡，因为平台限制,产品的功能体验不一致,会给人产生一种严重的割裂感
- 体验不一致,根本的原因是平台造成的

#### 为什么选择苹果生态
- 稳定、可靠。比windows稳定几个数量级,可以放心地在苹果生态中工作和学习
- 从底层打通数据链路,数据可以高效的互联互通
- 体验一致,因为是在统一的苹果生态下,使用统一的标准。
- 发展时间最长，生态较为完善，用户群体足够大。虽然现在(2024)某些产品也有生态,但是才刚刚起步阶段，且给我的感觉是"乱",核心功能没有做好,花大量的时间精力在无关紧要的功能上,没有好好的思考和打磨产品。
- 我不想再折腾了,多点时间留给美好的生活





