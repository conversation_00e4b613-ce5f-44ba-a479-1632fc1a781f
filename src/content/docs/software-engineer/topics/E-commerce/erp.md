---
title: 电商ERP
description: 电商ERP
template: doc
draft: true
lastUpdated: 2024-06-15 14:20:12
---
站群(Amazon、Shopify、店匠Shoplazza等) + 自营站点

**主要做的功能点:**
- 对接平台API,拉取原始订单(渠道)
- 渠道同步至ERP订单库的原始订单表
- 原始订单表转为ERP订单(所有的业务都是基于ERP订单来处理的)
- 转成ERP订单后会跑自动规则
  - 黑名单规则
  - sku匹配规则
  - 物流规则
  - 等等
  
  如果都跑成功了就会获取到物流单号
- 获取到物流单号后,就进入到拣货、打包、发货的流程
- 如果以上业务都正常,可以等用户确认收货或者系统自动收货
  - 自动收货,会通过channel服务自动同步过来

**转成ERP订单后,ERP订单可以做的一些工作:**
- 编辑订单信息
- 拆单,将一个大单拆成多个单,然后分多个包裹发出去。这样不会因为某一个商品拖太久而导致整个订单发不出去;应为也是为了打包方便,因为都是系统自动化操作,通过一个规则限制可以流程化
- 售后

**售后服务:**
- 退某一个商品
- 修改商品数量
- 新增新的商品(例如一些赠品)
- 整单退
- 部分商品退

退款对接了Paypal等支付机构、也支持线下退

**如果是用户收到货后不满意:**
如果用户收到货后不满意,和客服沟通后，我们会生成一个取货单，取货发回仓库，那就涉及到退货包裹的拆,在入库的操作。这写操作会把相关的事件消息同步到ERP订单模块。


**商品**
···——>···——>···——>刊登(*)

侵权商品下架等等

  