---
title: Base理论
description: Base理论
template: doc
lastUpdated: 2024-04-06 21:50:42
---
- BA(Basically Available)：基本可用
- S(Soft State)：软状态
- E(Eventually Consistent)：最终一致性

![](/software-engineer/drawio/Moatkon-Base.drawio.svg)

Base 理论的核心思想是**最终一致性**，即使无法做到强一致性(Strong Consistency)，但每个应用都可以根据自身的业务特点，采用适当的方式来使系统达到最终一致性(Eventual Consistency)。 接下来我们着重对 Base 理论中的三要素进行讲解。

### 软状态
软状态可以对应 ACID 事务中的原子性，在 ACID 的事务中，实现的是强制一致性，要么全做要么不做，所有用户看到的数据一致。其中的原子性(Atomicity)要求多个节点的数据副本都是一致的，强调数据的一致性。 原子性可以理解为一种“硬状态”，软状态则是允许系统中的数据存在中间状态，并认为该状态不影响系统的整体可用性，即允许系统在多个不同节点的数据副本存在数据延时。

### 最终一致性
数据不可能一直是软状态，必须在一个时间期限之后达到各个节点的一致性，在期限过后，应当保证所有副本保持数据一致性，也就是达到数据的最终一致性。 在系统设计中，最终一致性实现的时间取决于网络延时、系统负载、不同的存储选型、不同数据复制方案设计等因素。

### CAP及Base的关系
Base理论是在CAP上发展的，CAP理论描述了分布式系统中数据一致性、可用性、分区容错性之间的制约关系，当你选择了其中的两个时，就不得不对剩下的一个做一定程度的牺牲。 Base理论则是对 CAP 理论的实际应用，也就是在分区和副本存在的前提下，通过一定的系统设计方案，放弃强一致性，实现基本可用，这是大部分分布式系统的选择，比如 NoSQL 系统、微服务架构。

在这个前提下，如何把基本可用做到最好，就是分布式工程师们追求的, 这也是我一直追求的

:::note[ACID?]
ACID 是一种强一致性模型，强调原子性、一致性、隔离性和持久性，主要用于在数据库实现中。Base 理论面向的是高可用、可扩展的分布式系统，ACID 适合传统金融等业务，在实际场景中，不同业务对数据的一致性要求不一样，ACID 和 Base 理论往往会结合使用。
:::
