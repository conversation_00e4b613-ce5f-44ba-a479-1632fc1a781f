---
title: 分布式链路追踪
description: 分布式链路追踪
template: doc
lastUpdated: 2023-12-24 23:04:38
---
分布式链路追踪就是将一次分布式请求还原成调用链路，将一次分布式请求的调用情况集中展示，比如各个服务节点上的耗时、请求具体到达哪台机器上、每个服务节点的请求状态等等。

### 核心要解决的问题
- 故障快速定位：可以通过调用链结合业务日志快速定位错误信息。
- 链路性能可视化：各个阶段链路耗时、服务依赖关系可以通过可视化界面展现出来。
- 链路分析：通过分析链路耗时、服务依赖关系可以得到用户的行为路径，汇总分析应用在很多业务场景。

### 概念
- 追踪ID（traceid）: 用于查出本次请求调用的所有服务
- 跨度ID（spanid）: 用来记录调用顺序

一个完整请求链路的追踪ID（traceid）用于查出本次请求调用的所有服务，每一次服务调用的跨度ID（spanid）用来记录调用顺序

### 如何跨进程传递 context


参考:
https://www.cnblogs.com/whuanle/p/14321107.html