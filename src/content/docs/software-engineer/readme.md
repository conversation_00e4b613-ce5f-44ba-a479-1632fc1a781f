---
title: README
description: 软件工程师
template: doc
tableOfContents: false
lastUpdated: 2025-06-02 16:07:55
---
#### 主要路线
学习、总结、输出、Positive cycle

#### 面向的群体
- 对代码感兴趣
- 正在学习编程
- 进阶

<!-- #### 计划
- [ ] 先从基本的技术写起,不会太深入,而是力求简单易理解,我主要是读者自己可以意识到**原来如此,明白了**
- [ ] ~~待定,还没想好~~ -->


#### 为什么要写软件工程师相关的内容
1. 可以让我对基础技术的理解更加透彻
3. 期望通过护城河系列聚集一批志同道合的人
4. 帮助他人,帮助自己

#### 内容
内容来源:
- 互联网,如有雷同,[点此联系](mailto:<EMAIL>?subject=内容雷同&amp;body=麻烦请告知内容雷同的url及内容截图,moatkon会及时处理)
- 个人经验、理解

内容难免会出现错误,如有错误,麻烦指出:
- [内容勘误](mailto:<EMAIL>?subject=内容勘误&amp;body=如果可以的话,麻烦通过截图并标注的形式来直接指出错误的地方)
- [其他联系方式](/contact)
<!-- - 如果有Github账号,可以直接在内容下方评论 -->

<!-- #### 分享
在这里分享一个[如何写简历](https://www.youtube.com/watch?v=w1IQx8hEnTU) -->

<!-- #### 技术内容写作框架
1. 底层实现原理(如有)
2. 基于实现方式进一步引申出其他知识点——将知识串起来
3. 总结(如有值得总结的部分) -->

<!-- 阿里云藏经阁 https://developer.aliyun.com/ebook/index/ebook-classify-3-ali__0_0_0_1?spm=a2c6h.20345107.ebook-index.10.62fe29840oz8dS#ebook-list -->
