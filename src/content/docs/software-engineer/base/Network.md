---
title: 网络
description: 网络
template: doc
lastUpdated: 2023-12-13 22:36:00
---
## TCP三次握手
1. 第一次握手：客户端给服务器发送一个 SYN 报文。
2. 第二次握手：服务器收到 SYN 报文之后，会应答一个 SYN+ACK 报文。
3. 第三次握手：客户端收到 SYN+ACK 报文之后，会回应一个 ACK 报文。
4. 服务器收到 ACK 报文之后，三次握手建立完成。


#### 三次握手的作用
1. 是为了确认双方的接收与发送能力是否正常。
2. 指定自己的初始化序列号,为后面的可靠传送做准备

三次握手的一个重要功能是客户端和服务端交换ISN(Initial Sequence Number), 以便让对方知道接下来接收数据的时候如何按序列号组装数据。
如果ISN是固定的，攻击者很容易猜出后续的确认号，因此 ISN 是动态生成的。

#### 半连接队列
服务器第一次收到客户端的 SYN 之后，就会处于 SYN_RCVD 状态，此时双方还没有完全建立其连接，服务器会把此种状态下请求连接放在一个队列里，我们把这种队列称之为**半连接队列**。当然还有一个全连接队列，就是已经完成三次握手，建立起连接的就会放在全连接队列中。如果队列满了就有可能会出现丢包现象。

<!-- ## 四次挥手
> 关闭TCP连接时需要发送四个包，所以叫四次挥手

四次挥手也一样，千万不要对方一个 FIN 报文，我方一个 ACK 报文，再我方一个 FIN 报文，我方一个 ACK 报文。然后结束，最好是说的详细一点，例如想下面这样就差不多了，要把每个阶段的状态记好，我上次面试就被问了几个了，呵呵。我答错了，还以为自己答对了，当时还解释的头头是道，呵呵。
刚开始双方都处于 establised 状态，假如是客户端先发起关闭请求，则：
1、第一次挥手：客户端发送一个 FIN 报文，报文中会指定一个序列号。此时客户端处于FIN_WAIT1状态。
2、第二次握手：服务端收到 FIN 之后，会发送 ACK 报文，且把客户端的序列号值 + 1 作为 ACK 报文的序列号值，表明已经收到客户端的报文了，此时服务端处于 CLOSE_WAIT状态。
3、第三次挥手：如果服务端也想断开连接了，和客户端的第一次挥手一样，发给 FIN 报文，且指定一个序列号。此时服务端处于 LAST_ACK 的状态。
4、第四次挥手：客户端收到 FIN 之后，一样发送一个 ACK 报文作为应答，且把服务端的序列号值 + 1 作为自己 ACK 报文的序列号值，此时客户端处于 TIME_WAIT 状态。需要过一阵子以确保服务端收到自己的 ACK 报文之后才会进入 CLOSED 状态
5、服务端收到 ACK 报文之后，就处于关闭连接了，处于 CLOSED 状态。 -->

![](/software-engineer/network/3or4.webp)
> 该图片来自互联网

## WebSocket 与 Socket、TCP、HTTP 的关系及区别

Websocket 是一种新型的协议，它允许客户端和服务器之间建立实时的双向通信通道。相比于 HTTP 协议来说，它具有以下优点：
- 节约资源： 相比于轮询机制，Websocket只需要建立一次连接即可实现实时通信，这样可以减少服务器的压力和网络流量。
- 兼容性： Websocket 协议能够支持所有主流的浏览器和移动设备。
Websocket 协议在实时通信、在线聊天、多人游戏、实时监控等场景下具有广泛的应用价值。
- 实时性： Websocket支持服务器主动向客户端推送消息，使得客户端能够实时接收到服务器的事件和数据变化。
- 双向性： Websocket支持全双工通信，即客户端和服务器可以同时发送和接收数据。

#### TCP和WebSocket 的区别
- 层次结构：**TCP是传输层协议**,而WebSocket 是应用层协议。
- 协议特点：TCP 是一种面向连接的协议，使用三次握手建立连接，提供可靠的数据传输。而 WebSocket 是一种无状态的协议，使用 HTTP 协议建立连接，可以进行双向通信，WebSocket 的数据传输比 TCP 更加轻量级。
- 数据格式：TCP 传输的数据需要自定义数据格式，而 WebSocket 可以支持多种数据格式，如 JSON、XML、二进制等。WebSocket 数据格式化可以更好的支持 Web 应用开发。
- 连接方式：TCP 连接的是物理地址和端口号，而 WebSocket 连接的是 URL 地址和端口号

[参考: WebSocket 与 Socket、TCP、HTTP 的关系及区别](https://apifox.com/apiskills/websocket-socket-tcp-http/)


## 网络7层协议/OSI七层模型
OSI 模型是一个描述网络功能的概念框架。简单来说，OSI 模型标对计算机系统彼此之间发送信息的方式进行了标准化。

OSI 只是一个模型，一个工具，并不是一组规则。

![](/software-engineer/network/osi.png)
- 应用层: 应用层是计算机用户，以及各种应用程序和网络之间的接口，其功能是直接向用户提供服务，完成用户希望在网络上完成的各种工作。
- 表示层: 表示层负责数据格式的转换，将应用处理的信息转换为适合网络传输的格式，或者将来自下一层的数据转换为上层能处理的格式。
- 会话层: 虽然已经可以实现给正确的计算机，发送正确的封装过后的信息了。但我们总不可能每次都要调用传输层协议去打包，然后再调用IP协议去找路由，所以我们要建立一个自动收发包，自动寻址的功能。于是会话层出现了：它的作用就是建立和管理应用程序之间的通信。
- 传输层: 当发送大量数据时，很可能会出现丢包的情况，另一台电脑要告诉是否完整接收到全部的包。如果缺了，就告诉丢了哪些包，然后再发一次，直至全部接收为止。传输层的主要功能就是：监控数据传输服务的质量，保证报文的正确传输。
- 网络层: 计算机网络中如果有多台计算机，怎么找到要发的那台？如果中间有多个节点，怎么选择路径？这就是路由要做的事。该层的主要任务就是：通过路由选择算法，为报文(该层的数据单位，由上一层数据打包而来)通过通信子网选择最适当的路径。这一层定义的是IP地址，通过IP地址寻址，所以产生了IP协议。
- 数据链路层: 该层的主要功能就是：通过各种控制协议，将有差错的物理信道变为无差错的、能可靠传输数据帧的数据链路。它的具体工作是接收来自物理层的位流形式的数据，并封装成帧，传送到上一层；同样，也将来自上层的数据帧，拆装为位流形式的数据转发到物理层。这一层的数据叫做帧。
- 物理层: 解决两个硬件之间怎么通信的问题，常见的物理媒介有光纤、电缆、中继器等。它主要定义物理设备标准，如网线的接口类型、光纤的接口类型、各种传输介质的传输速率等。它的主要作用是传输比特流(就是由1、0转化为电流强弱来进行传输，到达目的地后在转化为1、0，也就是我们常说的数模转换与模数转换)。这一层的数据叫做比特。

#### 横向对比下TCP/IP4层模型、5层模型和OSI七层模型的差别
![](/software-engineer/network/network_layer_compare.png)





