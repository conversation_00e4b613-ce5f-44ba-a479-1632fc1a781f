---
title: Docker
description: Docker
template: doc
# banner:
#   content: |
#     学习中
lastUpdated: 2024-04-11 00:38:01
---
[upupor](https://github.com/yangrunkang/upupor) 就是使用Docker部署的.

因为Docker属于运维范畴,我不能说对Dokcer有多熟悉,但是基本的使用还是OK,例如搭建开发环境,部署应用之类的。

### 使用Docker可以带来哪些好处
1. 更高效的利用系统资源。由于容器不需要进行**硬件虚拟**以及运**行完整操作系统**等额外开销，Docker 对系统资源的利用率更高。无论是应用执行速度、内存损耗或者文件存储速度，都要比传统虚拟机技术更高效。因此，相比虚拟机技术，一个相同配置的主机，往往可以运行更多数量的应用。
2. 更快速的启动时间。传统的虚拟机技术启动应用服务往往需要数分钟，而 Docker 容器应用，由于**直接运行于宿主内核，无需启动完整的操作系统**，因此可以做到秒级、甚至毫秒级的启动时间。大大的节约了开发、测试、部署的时间。
3. 一致的运行环境。开发过程中一个常见的问题是环境一致性问题。由于开发环境、测试环境、生产环境不一致，导致有些 bug 并未在开发过程中被发现。而 Docker 的镜像提供了除内核外完整的运行时环境，确保了应用运行环境一致性。
4. 持续交付和部署。使用 Docker 可以通过定制应用镜像来实现持续集成、持续交付、部署。
5. 更轻松的迁移。由于 Docker 确保了执行环境的一致性，使得应用的迁移更加容易
6. 更轻松的维护和扩展。Docker 使用的分层存储以及镜像的技术，使得应用重复部分的复用更为容易，也使得应用的维护更新更加简单，基于基础镜像进一步扩展镜像也变得非常简单。

### 基本概念
#### 镜像
Docker 镜像 是一个特殊的文件系统，除了提供容器运行时所需的程序、库、资源、配置等文件外，还包含了一些为运行时准备的一些配置参数（如匿名卷、环境变量、用户等）。镜像 不包含 任何动态数据，其内容在构建之后也不会被改变。

#### 容器
镜像（Image）和容器（Container）的关系，就像是面向对象程序设计中的 类 和 实例 一样，镜像是静态的定义，容器是镜像运行时的实体。容器可以被创建、启动、停止、删除、暂停等。

容器的实质是进程，但与直接在宿主执行的进程不同，容器进程运行于属于自己的独立的 命名空间。因此容器可以拥有自己的 root 文件系统、自己的网络配置、自己的进程空间，甚至自己的用户 ID 空间。容器内的进程是运行在一个隔离的环境里，使用起来，就好像是在一个独立于宿主的系统下操作一样。

#### 仓库
镜像构建完成后，可以很容易的在当前宿主机上运行，但是，如果需要在其它服务器上使用这个镜像，我们就需要一个集中的存储、分发镜像的服务，Docker Registry 就是这样的服务。


### 使用
#### 获取镜像
```sh
docker pull [选项] [Docker Registry 地址[:端口号]/]仓库名[:标签]
```

#### 运行镜像
```sh
docker run -it --rm 镜像名 bash
```
- -it：这是两个参数，一个是 -i：交互式操作，一个是 -t 终端。我们这里打算进入 bash 执行一些命令并查看返回结果，因此我们需要交互式终端。
- --rm：这个参数是说容器退出后随之将其删除。默认情况下，为了排障需求，退出的容器并不会立即删除，除非手动 docker rm。我们这里只是随便执行个命令，看看结果，不需要排障和保留结果，因此使用 --rm 可以避免浪费空间。
- 镜像名：这是指用指定的镜像为基础来启动容器。
- bash：放在镜像名后的是 命令，这里我们希望有个交互式 Shell，因此用的是 bash。

#### 常用命令
- docker image ls 列出镜像
- docker system df 查看镜像、容器、数据卷所占用的空间
- docker image rm [选项] <镜像1> [<镜像2> ...] 删除镜像
- docker ps 查看docker中有哪些服务在运行
- docker logs -f `qa_order-service_1` 实时查看容器日志
- docker exec -it `840ec90eade2` /bin/sh 进入容器
- docker cp `qa_order-service_1:/app/order-web-1.0-SNAPSHOT.jar` ~  将docker中的文件拷贝出来
- docker update -m 1000M --memory-swap 1000M CONTAINER_ID 更新docker应用镜像占用内存
- docker stats 查看状态


### Compose
Compose 项目是 Docker 官方的开源项目，负责实现对 Docker 容器集群的快速编排。

Compose 恰好满足了这样的需求。它允许用户通过一个单独的 docker-compose.yml 模板文件（YAML 格式）来定义一组相关联的应用容器为一个项目（project）。

Compose 的默认管理对象是项目，通过子命令对项目中的一组容器进行便捷地生命周期管理。
