---
title: K8s
description: K8s
template: doc
# banner:
#   content: |
#     学习中
lastUpdated: 2023-12-19 16:03:03
---
K8s我理解的就是基于Docker做的一整套服务。在Java开发的角度,类似于Spring Cloud之于Spring的区别.

虽然 Docker 已经很强大了，但是在实际使用上还是有诸多不便，比如集群管理、资源调度、文件管理等等。

kubernetes 已经成为容器编排领域的王者，它是基于容器的集群编排引擎，具备扩展集群、滚动升级回滚、弹性伸缩、自动治愈、服务发现等多种特性能力

### Kubernetes 解决的核心问题
#### 服务发现和负载均衡
Kubernetes 可以使用 DNS 名称或自己的 IP 地址公开容器，如果到容器的流量很大，Kubernetes 可以负载均衡并分配网络流量，从而使部署稳定

#### 自动部署和回滚
您可以使用 Kubernetes 描述已部署容器的所需状态，它可以以受控的速率将实际状态更改为所需状态。例如，您可以自动化 Kubernetes 来为您的部署创建新容器，删除现有容器并将它们的所有资源用于新容器

#### 自动二进制打包
Kubernetes 允许您指定每个容器所需 CPU 和内存（RAM）。当容器指定了资源请求时，Kubernetes 可以做出更好的决策来管理容器的资源

#### 自我修复
Kubernetes 重新启动失败的容器、替换容器、杀死不响应用户定义的运行状况检查的容器，并且在准备好服务之前不将其通告给客户端

#### 密钥与配置管理
Kubernetes 允许您存储和管理敏感信息，例如密码、OAuth 令牌和 ssh 密钥。您可以在不重建容器镜像的情况下部署和更新密钥和应用程序配置，也无需在堆栈配置中暴露密钥