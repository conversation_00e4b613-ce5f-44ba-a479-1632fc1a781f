---
title: 效果数据拉取
description: 效果数据拉取
template: doc
draft: true
lastUpdated: 2024-06-15 14:20:12
---
#### 项目简易程度
常规

#### 项目难点
- 需要拉取的数据来源多
- 有时效要求
- 告警。如果在规定时间未拉取完成或者拉取异常要告警
- 失败重试

#### 解决方案
- 数据来源多意味着配置存在多样性。使用json来承载配置,使用枚举来锁定来源配置类,来统一json与对象之间的转化逻辑
- 抽象数据逻辑,每个数据拉取源继承抽象逻辑来实现自己的数据拉取逻辑
- 为了在有限的时间内将数据拉取完,使用了线程池,使用的是ForkJoinPool
- 告警逻辑是统一在抽象逻辑层做统一实现
- 失败重试逻辑,如果平台SDK有重试实现则使用平台的重试机制,如果没有则使用自己做的统一重试逻辑。在这里做了一个告警优化,因为部分失败场景是网络异常的告警,一般重试几次就可以解决,所以这里设定的阈值为如果超过总重试次数的一半再告警,可以大大减少告警量
- 因为数据量巨大(千万级别的数据量)且数据使用方为大数据,所以通过Kafka来承载拉取的数据,大数据来消费
