---
title: 搜索广告
description: 搜索广告
template: doc
draft: false
lastUpdated: 2025-05-31 09:32:35
---
基于关键词的广告投放。用户可以见的到效果是在搜索引擎搜索,如果命中我们的关键词,并且广告出价对于广告平台也合适,那么我们投放的商品就会展示在搜索结果页,点击所有结果会到商品的落地页Landing Page

### 背景
业务人员需要人工在GoogleAds平台创建广告系列。创建一个广告系列涉及多个页面的表单且部分字段有字符、长度等限制,人工配置效率低

### 业务诉求
业务期望他们提供一个模板,让技术人员根据模板来批量生成广告系列

### 简单的交互
![](/software-engineer/drawio/Moatkon-AdvertisingSearch.drawio.svg)

<!-- ### 简易程度
中上,难点集中在对GoogleAPI的熟悉程度。如果是新人第一次接触API,则需要不断的去测试、修正,很耗新力。如果熟悉了,则可以节省大量的调研时间,这样就会有充足的时间来设计和编码。 -->

### 项目难点
- 最大的困难其实不是编码,是对Google搜索广告的SDK不是很熟悉,需要大量的调研及测试
- 时效性,需要在规定时间生成好所有的广告系列
- 编码,需要写出易维护、拓展性高的代码
- 对公共逻辑的抽象
#### GoogleAds SDK熟悉
我采用的方式是人工在线上创建广告系列,再使用SDK创建一遍,对比有无差异,如果无差异说明SDK的理解和界面是一致的,否则就是不一致的,那么就可以缩小范围了。在小范围内再去看Google API文档,就可以很高效的解决这个问题

#### 时效性
核心是使用**多线程**来处理。使用了线程池。

在2个地方使用了多线程:
- 从关键词中台将词拉取到词库。落库时使用Spring的表达式对象来计算,符合要求的数据才入库
- 投放任务。因为存在多个模板,所以使用多线程来处理

#### 编码
##### 主要使用的是责任链模式,根据广告系列的组成来划分不同的责任链:
- 广告系列预算责任链
- 广告系列责任链
- 广告组责任链
- 广告组广告责任链

责任链的类型主要分为2大类:
- 替换模板变量的责任链。基于自定义的setter接口来实现相应的功能
  ```java
  public interface SetFieldHandler<T, P> {
    void setter(T t, P p);
  } 
  ```
- 构建GoogleAdsOperation的责任链。基于自定义的build接口来实现相应的功能
  ```java
  public interface MutateOperationBuilder<R, P> {
      List<R> build(ServingModel servingModel, P p);
  }
  ```

用责任链的原因: 可以快速定位代码、新功能拓展、复用(因为粒度小,可以复用逻辑)

##### 对象划分: Entity -> Bo -> Model
- Entity: 对应的是DB对象
- Bo: 是业务对象,即Business Object,在这一层会统一校验数据的正确性,如果有错误,立马终止投放流程。
- Model: 这个在Bo的基础上直接封装为投放模型,基于投放模型做业务可以保持代码整体的稳定性

为什么要基于模型来做,因为期望可以做到以下几点:
- 屏蔽不同广告渠道投放的数据,所有渠道的投放都基于该模型进行操作
- 基于模型驱动的开发方式，高内聚低耦合,核心业务逻辑在模型内完成
- 对业务流程和规则可以更好的抽象，提升代码的扩展性和开发效率


#### 对公共逻辑的抽象
主要是抽象命名组装逻辑、GoogleAds请求构造器逻辑

逻辑抽象其实没有大家想象的那么难,主要是看你对业务的理解程度,理解的好,抽象是自然而然的事情
