---
title: 从哪些地方可以优化现有系统?
description: 从哪些地方可以优化现有系统?
template: doc
lastUpdated: 2024-01-21 23:48:00
# tableOfContents: 
#     minHeadingLevel: 1
#     maxHeadingLevel: 5
---
### 写这篇内容的背景
业务发展,需要承载更多的流量。对主流程做了梳理,并提出优化目标

### 优化前系统的一些表现
- 部分接口耗时过长
- 频繁有慢SQL告警

### 策略
优先解决主流程的问题,后面再去逐步解决非核心流程的问题。

### 定位方法
- 根据SkyWalking找出耗时长的接口,根据链路再找出耗时长的操作
- 根据慢SQL定位到具体的代码行
- 走查代码。我自己在做这件事情的时候,核心接口的代码大概都过了一遍

### 具体优化措施
- 慢SQL
  - 索引: 在DB层面,主要是通过explain解释计划查看是否有使用到索引。如果在有索引的情况下存在没有使用到索引的情况,要检查自己的SQL是否编写的正确,因为有些写法会让索引失效。如果没有索引,就直接加索引。加索引尽量在区分度高的字段加,如果区分度不高,对DB来说反而是一种压力。
  - 业务: 业务需求是否合理。最常见的就是api请求响应日志、数据导出、日志等场景,要和业务沟通有效期
  - 分批: CURD操作大量数据,应该分批操作。其实很简单,CURD都可以加limit最终限制下要操作的数据条数
- 缓存。堆内缓存、堆外缓存
- 索引
- 并行
- [限流](/software-engineer/best-practices/custom-annotations-to-limit-and-downgrade)
- 大接口拆分
- 分库
- 分表。水平分表(平铺,要防止数据倾斜)、垂直拆分(冷热数据分离)
- 服务拆分
- 动态扩缩容
- 重构
- 冗余存储
- 如果有锁,需要控制锁粒度
- 池化

优化的基本要求是无感,不能影响到已有的业务

### 还有很多其他的优化思路
1. 项目中有很多接口的职责不清晰,即有的接口多负责了与接口定义不相关事情,这部分应该做拆分,尽量让接口功能清晰,职责分离。
2. 项目中的api服务、定时任务、消息消费,严重耦合在一个项目中。应该做进一步拆分,公共部分只需要通过公共依赖来解决。最佳实践,我之前做过一个跨境电商系统,项目分的很细。toC是SOA服务,toB是MSOA服务。在SOA服务和MSOA服务又做了进一步拆分,分为聚合服务和原子服务。job是一个单独的服务,消息消费是一个单独的服务。
   > 这样做的好处是:
   > - 扩缩容的粒度可以控制的更细,不必因为某一个功能点需要承载大流量而把不需要扩容的功能也扩容了。因此,资源可以得到更加高效和合理的应用
   > - 服务架构可以驱使业务和功能接口设计更加合理。
   > - 单元测试覆盖率可以得到有效提升,代码质量渐渐可控
   > - 在做升级或者重构等技术优化,可以直接聚焦到一个点,而不用担心会影响到其他地方
   >
   > 弊端也是有的:
   > - 运维成本高,对网络和资源规划有一定的要求
   > - 因为拆分多个系统,系统之间的交互开始变得灵活且复杂。因某些业务对数据一致性有要求,就会有些兜底方案,比如使用MQ来解耦系统时会使用到的本地消息表等
3. 校验前置。将校验前置的目的是让线程尽快的结束,将资源快点释放,以此来提升系统的处理能力。还有另外一点,将校验前置,也可以尽量减少DB资源占用。在开发一个业务功能时,需要有一定的思考,如果做一步校验一步,虽然过程思路清晰,但是对于并发较高的项目来说是一种负担
4. 重构。在上面提到过,如果通过很多手段发现达不到预期目标,就要考虑重构。如果重构就有很多发挥空间,可以引入新的组件、使用新的技术等等。重构的基本要求是不影响现有功能。
5. [全链路超时梳理](/software-engineer/best-practices/timeout)