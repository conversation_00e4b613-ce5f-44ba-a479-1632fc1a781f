---
title: React 4/n
description: React 4/n
template: doc
lastUpdated: 2025-02-01 12:06:03
tableOfContents: false
banner:
  content: 已经学习
---

React 组件使用 props 来互相通信。每个父组件都可以提供 props 给它的子组件，从而将一些信息传递给它。Props 可能会让你想起 HTML 属性，但你可以通过它们传递任何 JavaScript 值，包括对象、数组和函数。

:::tip
在声明 props 时， 不要忘记 `(` 和 `)` 之间的一对花括号 `{` 和 `}`  ：
```js
function Avatar({ person, size }) {
  // ...
}
```
这种语法被称为 “解构”，等价于于从函数参数中读取属性：

```js
function Avatar(props) {
  let person = props.person;
  let size = props.size;
  // ...
}
```

给 prop 指定一个默认值 
如果你想在没有指定值的情况下给 prop 一个默认值，你可以通过在参数后面写 = 和默认值来进行解构：

```js
function Avatar({ person, size = 100 }) {
  // ...
}
```

现在， 如果 `<Avatar person={...} />` 渲染时没有 `size` prop，  `size` 将被赋值为 100。

默认值仅在缺少 `size` prop 或 `size={undefined}` 时生效。 但是如果你传递了 `size={null}` 或 `size={0}`，默认值将 **不** 被使用。

:::


#### 使用 JSX `展开语法`传递 props
语法: ...  (展开语法是三个点)
```js
function Profile(props) {
  return (
    <div className="card">
      <Avatar {...props} />
    </div>
  );
}
```

####
一个组件可能会随着时间的推移收到不同的 props。 Props 并不总是静态的！Props 反映了组件在任何时间点的数据，并不仅仅是在开始时。

#### 摘要
- 要传递 props，请将它们添加到 JSX，就像使用 HTML 属性一样。
- 要读取 props，请使用 function Avatar({ person, size }) 解构语法。
- 你可以指定一个默认值，如 size = 100，用于缺少值或值为 undefined 的 props 。
- 你可以使用 <Avatar {...props} /> JSX 展开语法转发所有 props，但不要过度使用它！
- 像 <Card><Avatar /></Card> 这样的嵌套 JSX，将被视为 Card 组件的 children prop。
- Props 是只读的时间快照：每次渲染都会收到新版本的 props。
- 你不能改变 props。当你需要交互性时，你可以设置 state。


#### TODO NEXT
- [X] https://zh-hans.react.dev/learn/conditional-rendering
