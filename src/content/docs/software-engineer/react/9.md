---
title: React 9/n - 脱围机制（Escape Hatches）
description: React 9/n
template: doc
lastUpdated: 2025-02-01 11:57:31
tableOfContents: false
banner:
  content: 高级。暂时不接触
---
https://zh-hans.react.dev/learn/escape-hatches


有些组件可能需要控制和同步 React 之外的系统。例如，你可能需要使用浏览器 API 聚焦输入框，或者在没有 React 的情况下实现视频播放器，或者连接并监听远程服务器的消息。在本章中，你将学习到一些脱围机制，让你可以“走出” React 并连接到外部系统。大多数应用逻辑和数据流不应该依赖这些功能。

当你希望组件“记住”某些信息，但又不想让这些信息 触发新的渲染 时，你可以使用 ref：
```js
    const ref = useRef(0);
```
ref 就像组件的一个不被 React 追踪的秘密口袋。例如，可以使用 ref 来存储 timeout ID、DOM 元素 和其他不影响组件渲染输出的对象。


Effect 是 React 范式中的一种脱围机制。它们可以“逃出” React 并使组件和一些外部系统同步。如果没有涉及到外部系统（例如，需要根据一些 props 或 state 的变化来更新一个组件的 state），不应该使用 Effect。移除不必要的 Effect 可以让代码更容易理解，运行得更快，并且更少出错。

有两种常见的不必使用 Effect 的情况：
- 不必为了渲染而使用 Effect 来转换数据。
- 不必使用 Effect 来处理用户事件。
