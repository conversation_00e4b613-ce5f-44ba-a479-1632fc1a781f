---
title: React 3/n
description: React 3/n
template: doc
lastUpdated: 2025-01-30 12:24:16
tableOfContents: false
banner:
  content: 已经学习
---

https://zh-hans.react.dev/learn/describing-the-ui

- React 组件使用 props 来进行组件之间的通讯。每个父组件都可以通过为子组件提供 props 的方式来传递信息。props 可能会让你想起 HTML 属性，但你可以通过它们传递任何 JavaScript 的值，包括对象、数组、函数、甚至是 JSX!
  - ```js
        import { getImageUrl } from './utils.js'

        export default function Profile() {
        return (
            <Card>
            <Avatar
                size={100}
                person={{
                name: '<PERSON><PERSON><PERSON>',
                imageId: 'YfeOqp2'
                }}
            />
            </Card>
        );
        }

        function Avatar({ person, size }) {
        return (
            <img
            className="avatar"
            src={getImageUrl(person)}
            alt={person.name}
            width={size}
            height={size}
            />
        );
        }

        function Card({ children }) {
        return (
            <div className="card">
            {children}
            </div>
        );
        }
    ```
- JSX 可以让你在 JavaScript 文件中编写类似 HTML 的标签语法，使渲染逻辑和内容展示维护在同一个地方。有时你会想在标签中添加一点 JavaScript 逻辑或引用一个动态属性。在这种情况下，你可以在 JSX 中使用花括号来为 JavaScript “开辟通道
  - ```js
        const person = {
            name: 'Gregorio Y. Zara',
            theme: {
                backgroundColor: 'black',
                color: 'pink'
            }
        };

        export default function TodoList() {
            return (
                <div style={person.theme}>
                <h1>{person.name}'s Todos</h1>
                <img
                    className="avatar"
                    src="https://i.imgur.com/7vQD0fPs.jpg"
                    alt="Gregorio Y. Zara"
                />
                <ul>
                    <li>Improve the videophone</li>
                    <li>Prepare aeronautics lectures</li>
                    <li>Work on the alcohol-fuelled engine</li>
                </ul>
                </div>
            );
        }
    ```
- React 允许你将标签、CSS 和 JavaScript 组合成自定义“组件”，即 应用程序中可复用的 UI 元素
- React 最为重视交互性且使用了相同的处理方式：React 组件是一段可以 使用标签进行扩展 的 JavaScript 函数
- 导出组件。export default 前缀是一种 JavaScript 标准语法（非 React 的特性）。它允许你导出一个文件中的主要函数以便你以后可以从其他文件引入它。
- **React 组件是常规的 JavaScript 函数**，但 **组件的名称必须以大写字母开头**，否则它们将无法运行！
  - ```js
        <section> 是小写的，所以 React 知道我们指的是 HTML 标签。
        <Profile /> 以大写 P 开头，所以 React 知道我们想要使用名为 Profile 的组件。
    ```
- ```js
    // 这个组件返回一个带有 src 和 alt 属性的 <img /> 标签。<img /> 写得像 HTML，但实际上是 JavaScript！这种语法被称为 JSX，它允许你在 JavaScript 中嵌入标签。
    // 返回语句可以全写在一行上，如下面组件中所示：
    return <img src="https://i.imgur.com/MK3eW3As.jpg" alt="Katherine Johnson" />;

    // 但是，如果你的标签和 return 关键字不在同一行，则必须把它包裹在一对括号中，如下所示：
    return (
        <div>
            <img src="https://i.imgur.com/MK3eW3As.jpg" alt="Katherine Johnson" />
        </div>
    );
    // 没有括号包裹的话，任何在 return 下一行的代码都 将被忽略！
  ```
- 🔴 永远不要在组件中定义组件.
  - ```js
        export default function Gallery() {
            // 🔴 永远不要在组件中定义组件
            function Profile() {
                // ...
            }
            // ...
        }
    ```
- ```js
    import Gallery from './Gallery.js';
    import Gallery from './Gallery';
    // 无论是 './Gallery.js' 还是 './Gallery'，在 React 里都能正常使用，只是前者更符合 原生 ES 模块。
  ```
- 默认导出 vs 具名导出
  ![alt text](/software-engineer/react/3-1.png)
  同一文件中，有且仅有一个默认导出，但可以有多个具名导出！
  :::tip
    为了减少在默认导出和具名导出之间的混淆，一些团队会选择只使用一种风格（默认或者具名），或者禁止在单个文件内混合使用。这因人而异，选择最适合你的即可！
  :::

---
JSX 是 JavaScript 语法扩展,JSX规则:
- 只能返回一个根元素
  > <> 和 </> 元素,这个空标签被称作 Fragment
- 标签必须闭合 
- 使用驼峰式命名法给 ~~所有~~ 大部分属性命名！
  - 在这里可以找html与jsx属性的写法映射: https://zh-hans.react.dev/reference/react-dom/components/common
  - 转换器: https://transform.tools/html-to-jsx
- 在 JSX 中通过大括号使用 JavaScript
  - 使用引号传递字符串
  - 用 `{` 和 `}` 替代 `"` 和 `"` 以使用 JavaScript 变量
  - 大括号内的任何 JavaScript 表达式都能正常运行，包括像 formatDate() 这样的函数调用
  - 可以在哪使用大括号
    - 用作 JSX 标签内的文本：`<h1>{name}'s To Do List</h1>` 是有效的，但是 `<{tag}>Gregorio Y. Zara's To Do List</{tag}>` 无效。
    - 用作紧跟在 = 符号后的 属性：src={avatar} 会读取 avatar 变量，但是 src="{avatar}" 只会传一个字符串 {avatar}。
- 使用 “双大括号”：JSX 中的 CSS 和 对象。除了字符串、数字和其它 JavaScript 表达式，你甚至可以在 JSX 中传递对象。对象也用大括号表示，例如 { name: "Hedy Lamarr", inventions: 5 }。因此，为了能在 JSX 中传递，你**必须用另一对额外的大括号**包裹对象：person={{ name: "Hedy Lamarr", inventions: 5 }}。
  > 所以当你下次在 JSX 中看到 {{ 和 }}时，就知道它只不过是包在大括号里的一个对象罢了！
  :::tip
    内联 style 属性 使用驼峰命名法编写。例如，HTML `<ul style="background-color: black">` 在你的组件里应该写成 `<ul style={{ backgroundColor: 'black' }}>`。
  :::

### TODO NEXT
- [X] https://zh-hans.react.dev/learn/passing-props-to-a-component
