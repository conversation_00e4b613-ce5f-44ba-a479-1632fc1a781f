---
title: React 5/n - 条件/列表渲染
description: React 5/n
template: doc
lastUpdated: 2025-01-31 13:12:14
tableOfContents: false
banner:
  content: 已经学习
---

### 条件渲染
- 在 React 中，你可以通过使用 JavaScript 的 `if` 语句、`&&` 和 `? :` 运算符来选择性地渲染 JSX
- 选择性地返回 null。在一些情况下，你不想有任何东西进行渲染，可以直接返回 null。
  > 实际上，在组件里返回 null 并不常见，因为这样会让想使用它的开发者感觉奇怪。通常情况下，你可以在父组件里选择是否要渲染该组件。
- 在 React 里，标签也是你代码中的一部分，所以你可以使用变量和函数来整理一些复杂的表达式。
- 当 JavaScript `&&` 表达式 的左侧（我们的条件）为 true 时，它则返回其右侧的值（在我们的例子里是勾选符号）。但条件的结果是 false，则整个表达式会变成 false。在 JSX 里，React 会将 false 视为一个“空值”，就像 null 或者 undefined，这样 React 就不会在这里进行任何渲染。
- 使用 `let` 进行重复赋值

### 列表渲染
在 React 中使用 filter() 筛选需要渲染的组件和使用 map() 把数组转换成组件数组。

```js
    const listItems = people.map(person => <li key={idXXX}>{person}</li>);
    const chemists = people.filter(person =>
        person.profession === '化学家'
    );
```

:::tip[直接放在 map() 方法里的 JSX 元素一般都需要指定 key 值！]
这些 key 会告诉 React，每个组件对应着数组里的哪一项，所以 React 可以把它们匹配起来。这在数组项进行移动（例如排序）、插入或删除等操作时非常重要。一个合适的 key 可以帮助 React 推断发生了什么，从而得以正确地更新 DOM 树。

> 用作 key 的值应该在数据中提前就准备好，而不是在运行时才随手生成
:::

> 一个精心选择的 key 值所能提供的信息远远不止于这个元素在数组中的位置。即使元素的位置在渲染的过程中发生了改变，它提供的 key 值也能让 React 在整个生命周期中一直认得它


> 有一点需要注意，组件不会把 key 当作 props 的一部分。Key 的存在只对 React 本身起到提示作用。如果你的组件需要一个 ID，那么请把它作为一个单独的 prop 传给组件： `<Profile key={id} userId={id} />`。
