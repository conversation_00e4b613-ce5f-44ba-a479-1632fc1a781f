---
title: React 2/n
description: React 2/n
template: doc
lastUpdated: 2025-01-29 23:37:53
tableOfContents: false
banner:
  content: 已经学习
---
https://zh-hans.react.dev/learn/thinking-in-react


### 知识点
- state 只是为交互提供的保留功能，即数据会随着时间变化
- 记住：React 使用单向数据流，通过组件层级结构从父组件传递数据至子组件。要搞清楚哪个组件拥有哪个 state。
- 用 `useState()` Hook 为组件添加`state`。Hook 可以“钩住”组件的 渲染周期。

### 是否是state判别
- 随着时间推移 保持不变？如此，便不是 state。
- 通过 props 从父组件传递？如此，便不是 state。
- 是否可以基于已存在于组件中的 state 或者 props 进行计算？如此，它肯定不是state！

:::note
在 React 中有两种“模型”数据：props 和 state。下面是它们的不同之处:

- props 像是你传递的参数 至函数。它们使父组件可以传递数据给子组件，定制它们的展示。举个例子，Form 可以传递 color prop 至 Button。
- state 像是组件的内存。它使组件可以对一些信息保持追踪，并根据交互来改变。举个例子，Button 可以保持对 isHovered state 的追踪。


props 和 state 是不同的，但它们可以共同工作。父组件将经常在 state 中放置一些信息（以便它可以改变），并且作为子组件的属性 向下 传递至它的子组件。
:::


### NEXT TODO
- [X] 安装: https://zh-hans.react.dev/learn/installation

