---
title: 消息队列
description: 消息队列
template: doc
lastUpdated: 2024-04-01 13:38:43
---
#### 一、为什么会出现消息队列?
削峰、解耦

> 为啥不使用线程?线程是当前应用内的,限制太多

#### 二、使用消息队列会出现哪些问题
##### 1. 系统可用性降低
系统引入的外部依赖越多,越容易挂掉

##### 2. 系统复杂度提高
###### 消息的重复消费。哪些场景会产生重复的消息?
- 网络闪断
- ack失败,队列以为没有消费


**解决方案:**
- 业务端保持幂等,例如已取消的订单,又来了一个已取消的消息,则不处理,保持幂等
- 唯一的标识,类似uuid,对已经处理过的消息,不在做二次处理
- 消息表,使用msgId坐主键或者唯一约束

    
###### 2. 消息丢失

**消息会在哪些环节可能丢失?**
- 消息在传入过程中丢失
- 队列收到消息,暂存在内存中,还没来得及被消费,队列挂掉了,内存中的数据丢失
- 消费者消费到了这个消息,但是还没有来得及处理,消费者就挂了,而队列以为消息已经被处理

**解决方案:**

先入事务表(消息表)。 如果事务表也满了呢,根据业务日志找回,因为在实际开发过程中都会打印日志,找到对应的日志,重新写入mq

**2.1 针对RabbitMQ消息队列可以有以下措施解决:**

1. 对于生产者:
   - 开启RabbitMQ事务(同步,不推荐)
   - 开启confirm模式(异步推荐)
2. 对于队列: 开启RabbitMQ持久化
3. 对于消费者： 关闭RabbitMQ自动ack,采用手动ack
4. 数据一致性问题。使用消息表兜底,如果消息表兜底措施也有问题,人工介入,系统并不能解决所有的问题

#### 三、消息队列是天然的广播(发布)
消费者可各取所需(订阅)

#### 四、天然的涉及到分布式
队列的解耦、异步,就会催生天然的分布式

#### 五、Kafka 和 RabbitMQ 消息队列比对
1. 消息传递模型：
   
   RabbitMQ：RabbitMQ是一个消息代理,它实现了高级消息队列协议（AMQP）。它支持多种消息传递模型,包括点对点和发布-订阅模型。

   Kafka：Kafka是一个分布式流处理平台,主要用于处理实时数据流。它采用发布-订阅模型,消息被持久化保存在日志中,允许多个消费者以不同的速率消费消息。

2. 持久性：

   RabbitMQ：RabbitMQ默认将消息保存在内存中,可以配置为将消息持久化到磁盘。这使得RabbitMQ在一些情况下可能会有较低的持久性。
   
   Kafka：Kafka将消息持久化到磁盘,因此能够保证数据的持久性。它适用于需要高吞吐量和持久性的场景,如日志处理和事件溯源。

3. 适用场景：
   
   RabbitMQ：适用于传统的消息队列场景,如任务队列、事件驱动等。它提供了更多的消息处理模式,适合需要灵活性的应用。

   Kafka：适用于大规模的数据管道和实时数据处理,特别是在日志聚合、事件溯源、和流处理方面表现出色。

4. 性能：
   
   RabbitMQ：RabbitMQ的性能也很好,但在某些情况下可能会受到单一队列的限制,因此在需要水平扩展性的场景下可能需要一些额外的配置。

   Kafka：Kafka旨在提供高吞吐量和水平扩展性,适用于大规模的数据处理和分布式系统。

5. 一致性和可用性：

   RabbitMQ：RabbitMQ也提供了高可用性的配置选项,但可能需要一些复杂的设置来实现。

   Kafka：Kafka设计为具有高可用性和容错性,可以容忍节点故障。它保证消息的有序性和一致性。

<!-- - 消息传递模型: Kafka是一种分布式发布/订阅系统,遵从一般的MQ结构。它以日志为中心,消息被追加到日志的末尾,并由多个消费者以不同的消费组订阅这些消息。RabbitMQ是一个实现了AMQP（Advanced Message Queuing Protocol）标准的消息代理,支持多种消息传递模型,包括点对点和发布/订阅模型。
- 性能：Kafka在处理高并发日志方面具有很高的性能,每秒请求数可以达到数十万量级。而RabbitMQ的性能相对较低,每秒请求数一般为万级别。
- 适用场景：Kafka适用于处理活跃的流式数据和大容量的数据,例如日志数据、监控数据等。而RabbitMQ适用于实时的、对可靠性要求较高的消息传递,例如金融交易、订单处理等场景。
- 易用性：RabbitMQ相对更易于使用,它可以使用yum或docker安装,并且自带Web管理UI。而Kafka则需要依赖Zookeeper,并且使用起来相对较复杂。 -->
<!-- - 消息可靠性：RabbitMQ具有更高的可靠性,它支持消息的确认机制,可以确保消息被正确地传递和处理。而Kafka在消息可靠性方面相对较弱,它主要依赖于消费者自己处理消息的确认。 -->
<!-- - 消息持久化：Kafka具有更高的持久化能力,它支持将消息持久化到磁盘,并且可以设置消息的保留策略。而RabbitMQ在消息持久化方面相对较弱,它主要依赖于消息的可靠性机制来确保消息不会丢失。 -->

<!-- 
- 在吞吐量,
kafka具有高的吞吐量,内部采用消息的批量处理,zero-copy机制,数据的存储和获取是本地磁盘顺序批量操作,具有O(1)的复杂度,消息处理的效率很高。

rabbitMQ在吞吐量方面稍逊于kafka,他们的出发点不一样,rabbitMQ支持对消息的可靠的传递,支持事务,不支持批量的操作；基于存储的可靠性的要求存储可以采用内存或者硬盘。

- 在可用性方面,
rabbitMQ支持miror的queue,主queue失效,miror queue接管。

kafka的broker支持主备模式。

- 在集群负载均衡方面,
kafka采用zookeeper对集群中的broker、consumer进行管理,可以注册topic到zookeeper上；通过zookeeper的协调机制,producer保存对应topic的broker信息,可以随机或者轮询发送到broker上；并且producer可以基于语义指定分片,消息发送到broker的某分片上。

rabbitMQ的负载均衡需要单独的loadbalancer进行支持。 -->


<!-- 
Kafka 和 RabbitMQ 是可用于流处理的消息队列系统。数据流是需要高速处理的大容量、连续增量数据。

RabbitMQ 是一个分布式消息代理,从多个来源收集流式处理数据,然后将其路由到不同的目标进行处理。Apache Kafka 是一个流式处理平台,用于构建实时数据管道和流式处理应用程序。Kafka 提供了一个高度可扩展、容错和持久的消息收发系统,其功能比 RabbitMQ 更多

##### 区别1: 架构差异
在 RabbitMQ 和 Kafka 中,生产者和使用者的互动方式有所不同。在 RabbitMQ 中,生产者发送并监控消息是否到达目标使用者。另一方面,无论使用者是否检索消息,Kafka 生产者都会向队列发布消息。

可以将 RabbitMQ 视为接收邮件并将其传输给预定收件人的邮局。与此同时,Kafka 类似于图书馆,它在书架上整理生产者发布的不同类型消息。然后,使用者读取相应书架上的消息,并记住他们所读取的内容。 

RabbitMQ 通过简单的架构提供复杂的消息路由,而 Kafka 提供耐用的消息代理系统,可让应用程序处理流历史记录中的数据。

###### RabbitMQ 架构
RabbitMQ 代理允许使用以下组件进行低延迟和复杂的消息分配：
- 交换机接收来自生产者的消息并确定应将它们路由到何处
- 队列是接收来自交换机的消息并将其发送给使用者的存储空间
- 绑定是连接交换机和代理的路径

在 RabbitMQ 中,路由密钥是一种消息属性,用于将消息从交换机路由到特定队列。当生产者向交换机发送消息时,它会将路由密钥作为消息的一部分包含在内。然后,交换机使用此路由密钥来确定消息应传输到哪个队列。

###### Kafka 架构方法
Kafka 集群通过更复杂的架构提供高吞吐量流事件处理。以下是 Kafka 的一些关键组件：
- Kafka 代理是 Kafka 服务器,允许生产者将数据流式传输给使用者。 Kafka 代理包含主题及其相应的分区。 
- 主题是在 Kafka 代理中对相似数据进行分组的数据存储。 
- 分区是使用者所订阅主题中较小的数据存储空间。 
- ZooKeeper 是特殊的软件,用于管理 Kafka 集群和分区以提供容错流式处理。ZooKeeper 最近被 Apache Kafka Raft（KRaft）协议所取代。

Kafka 中的生产者为每条消息分配一个消息密钥。然后,Kafka 代理将消息存储在该特定主题的前导分区中。KRaft 协议使用共识算法来确定前导分区。 



##### 区别2: 消息处理方式
RabbitMQ 和 Apache Kafka 以不同的方式将数据从生产者转移到使用者。RabbitMQ 是通用消息代理,它优先考虑端到端消息传输。Kafka 是分布式事件流式处理平台,支持持续大数据的实时交换。


###### 消息使用
在 RabbitMQ 中,代理确保使用者收到消息。使用者应用程序扮演被动角色,等待 RabbitMQ 代理将消息推送到队列中。例如,银行应用程序可能会等待来自中央交易处理软件的短信提醒。

然而,Kafka 使用者更积极地读取和跟踪信息。当消息加入实体日志文件时,Kafka 使用者会跟踪他们读取的最后一条消息,并相应地更新偏移跟踪器。偏移跟踪器是读取消息后递增的计数器。使用 Kafka,生产者并不知道使用者会检索消息。 

###### 消息优先级
RabbitMQ 代理允许生产者软件使用优先队列升级某些消息。代理不是按先入先出的顺序发送消息,而是先处理更高优先级的消息,然后再处理普通消息。例如,零售应用程序可能每小时将销售交易排队一次。但是,如果系统管理员发出优先的备份数据库消息,则代理会立即发送该消息。

与 RabbitMQ 不同,Apache Kafka 不支持优先级队列。将所有消息分配到各自的分区时,该代理平等对待这些消息。 

###### 消息排序
RabbitMQ 按特定顺序发送消息和对其进行排队。除非有更高优先级的消息排入系统,否则使用者会按照消息的发送顺序接收消息。

同时,Kafka 使用主题和分区对消息进行排队。当生产者发送消息时,消息会进入特定的主题和分区。由于 Kafka 不支持直接的生产者与使用者交换,因此使用者以不同的顺序从分区中拉取消息。 

###### 消息删除
RabbitMQ 代理将消息路由到目标队列。读取后,使用者向代理发送确认（ACK）回复,然后代理将消息从队列中删除。

与 RabbitMQ 不同,Apache Kafka 将消息附加到日志文件中,该日志文件将一直留存到其保留期到期。这样,使用者可以在规定的时间内随时重新处理流式传输的数据。

##### 区别3: 性能
RabbitMQ 和 Kafka 都为其预期使用案例提供高性能的消息传输。但是,在消息传输容量方面,Kafka 的表现优于 RabbitMQ。

Kafka 每秒可以发送数百万条消息,因为它使用顺序磁盘 I/O 来实现高吞吐量消息交换。顺序磁盘 I/O 是一种存储系统,用于存储和访问来自相邻内存空间的数据,比随机磁盘访问更快速。

RabbitMQ 还可以每秒发送数百万条消息,但它需要多个代理才能达成此目标。通常,RabbitMQ 的性能为平均每秒处理数千条消息,如果 RabbitMQ 的队列拥挤,则处理可能会变慢。 

##### 区别4: 安全性
RabbitMQ 和 Kafka 允许应用程序安全地交换消息,但使用不同的技术。

RabbitMQ 附带管理工具,用于管理用户权限和代理安全。

同时,Apache Kafka 架构通过 TLS 和 Java 身份验证与授权服务（JAAS）提供安全的事件流。TLS 是一种加密技术,可防止消息被意外窃听,而 JAAS 控制哪个应用程序可以访问代理系统。
##### 区别5: 编程语言和协议
Kafka 和 RabbitMQ 都支持开发人员熟悉的各种语言、框架和协议。

在为 Kafka 和 RabbitMQ 构建客户端应用程序时,可以使用 Java 和 Ruby 编写代码。此外,Kafka 支持 Python 和 Node.js,而 RabbitMQ 支持 JavaScript、Go、C、Swift、Spring、Elixir、PHP 和 .NET。

Kafka 在 TCP 上使用二进制协议跨实时数据管道传输消息,而 RabbitMQ 默认支持高级消息队列协议（AMQP）。RabbitMQ 还支持诸如简单文本导向消息收发协议（STOMP）和 MQTT 之类的旧式协议来路由消息。

#### 六、什么时候使用Kafka和RabbitMQ
务必了解的是,RabbitMQ 和 Kafka 不是相互竞争的消息代理。两者都旨在支持不同使用案例中的数据交换,分别有其适用场景。

接下来,我们将讨论考虑采用 RabbitMQ 和 Kafka 的一些使用案例。

##### 事件流重放
Kafka 适用于需要重新分析所接收数据的应用程序。可以在保留期内多次处理流式数据或收集日志文件进行分析。

使用 RabbitMQ 进行日志聚合更具挑战性,因为消息一旦使用就会被删除。解决方法是重放来自生产者的已存储消息。

##### 实时数据流式处理
Kafka 以非常低的延迟流式传输消息,适用于实时分析流式数据。例如,可以将 Kafka 用作分布式监控服务,为在线事务处理实时发出提醒。

##### 复杂的路由架构
RabbitMQ 为要求模糊或路由场景复杂的客户端提供灵活性。例如,可以将 RabbitMQ 设置为将数据路由到具有不同绑定和交换机的不同应用程序。

##### 有效的消息传输
RabbitMQ 采用推送模型,这意味着生产者知悉客户端应用程序是否使用消息。该方法适用于在交换和分析数据时必须遵守特定顺序和传输保证的应用程序。 

##### 语言和协议支持
开发人员将 RabbitMQ 用于需要向后兼容诸如 MQTT 和 STOMP 等旧式协议的客户端应用程序。与 Kafka 相比,RabbitMQ 还支持更广泛的编程语言。

> [](https://aws.amazon.com/cn/compare/the-difference-between-rabbitmq-and-kafka/) -->

#### 六、消息的顺序消费
保证消息顺序消费的前提是生产者的消息是顺序生产的

顺序消费由消费者业务保证(Hash等操作都可以完成),例如: 对于订单的场景,可以让同一个订单消息根据某个规则(例如hash)都发到一个队列中。

###### 如果是多消费者呢?又如何保证消息消费的顺序性? `from chatGPT`
- 单一消费者： 如果你的应用程序结构允许，考虑使用单一消费者来处理消息。这样可以确保消息的顺序性。然而，这可能会影响系统的吞吐量。<span style="color:grey"> —— 在我的工作经历中,都是采用这种方式,虽然部署了多个job节点,但是实际触发运行的只要一个节点,即单线程消费。如果是并行任务,应该单独起一个job项目专门处理这一类的任务,通过多消费者来提升处理能力</span>
- 消息预处理： 在消费者处理消息之前，可以在多线程环境中进行预处理，将消息按照一定的规则排序，然后再交给各个线程处理。这样可以保持消息的顺序性。
- 多个队列，按顺序分发： 将消息分发到多个队列，每个队列由一个单独的消费者线程处理。确保消息根据某个规则（例如，消息的顺序号）发送到相应的队列。这样每个队列内的消息是有序的，但不同队列的消息可以并行处理。


> 在网上我看到一个观点,即业务系统真正需要的是**业务事件顺序**。
> 
> 要按业务事件顺序处理消息，您实际上并不需要全局锁或只启动单个consumer，或者只选举一个consumer leader，这些解决方案提供了线性化级别的一致性(Linearizability Consistency)，但是在大多数情况下它们太慢且不必要。 实际上，我们只关心因果级别的一致性(Causal Consistency), 只要能找出业务相关的消息，就可以按照事件顺序处理。 为此，我们可以在消费者端为那些因果相关的消息提供状态机，如果消息出现故障（过早），我们可以将其本地存储在“收件箱”中。 稍后，当延迟的消息到达时，我们将状态机触发到下一个状态，然后检查收件箱以查看是否存在下一条期望的消息，或者继续等待下一个消息。
>
> **自己的理解**: "收件箱"其实就是类似一种共享变量,所有的消费者在消费时都要来访问一下“收件箱”再做逻辑
> 
> 参考链接: https://danielw.cn/messaging-reliability-and-order-cn

#### 七、消息产生积压如何处理?
- 临时紧急扩容(如果是docker就很简单,再起几个容器) ——增加消费者
- 建多队列,写分发程序把queue中积压的数据分发到新建的队列中,然后在写多个指定的消费者,消费指定的新建的队列数据。处理完成后再恢复到之前的常规配置
- 消费者开启线程池加快消息处理速度
- 如果是RabbitMQ,可以扩大队列容积,提高堆积上限,采用惰性队列(在声明队列时设置属性 `x-queue-mode` 为 `lazy`,即惰性队列)
  > 惰性队列的特点:接收到消息后直接存储到磁盘而非内存;消费者要消费消息时才从磁盘读取到内存;支持百万条消息的存储