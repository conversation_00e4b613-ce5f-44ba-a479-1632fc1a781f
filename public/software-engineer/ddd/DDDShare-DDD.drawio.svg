<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="833px" height="591px" viewBox="-0.5 -0.5 833 591" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2024-03-10T11:52:36.777Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/22.0.2 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36&quot; etag=&quot;Syo14-oPpWI4T7V9naXY&quot; version=&quot;22.0.2&quot; type=&quot;device&quot; pages=&quot;3&quot;&gt;&lt;diagram name=&quot;DDD&quot; id=&quot;GFKkxeW_b9Flx3wovdCe&quot;&gt;3ZnBcpswEIafxkfPAEKyOCZOmk5n2s7Uh7RHGS1GDSBXlmM7T19hiwAG6nYmBBcfPPAvAvRp2Z+1J2ie7h8UW8efJYdk4jl8P0F3E88LAmy+c+FwEigOTsJKCX6S3FJYiBewomPVreCwqR2opUy0WNfFUGYZhLqmMaXkrn5YJJP6VddsBQ1hEbKkqT4KrmOrIkrLwEcQq9hemvr+KZCy4mA7k03MuNxVJHQ/QXMlpT5tpfs5JDm7gstp3IeO6OuNKcj03wyYf3nceZub787Ty7cM3F93Pz59ndqzPLNkaydsb1YfCgJKbjMO+UncCbrdxULDYs3CPLozS260WKeJDUcy03YRXZzviySZy0Sq47kQZ0Cj0OgbreQTVCIkpLCMTKQ5reIeQWnYVyQ7zQeQKWh1MIfYKCqSx+ac59j9XbmChFgtrixeYDVmk2b1euqSq9mwaP8Bs9fALBUHNd2AehYhTDeamcmpXtnD8dPGHhEUIP5G7P0z9i5usHcxarInfbFHHex/yuWIuGOCr4y735XzhywcEfhZcG0Jjxvgu2ibKeo6UpaIVWa2QwPEDEC3OQhh7PDGBlLBeT68dW3K1XNesReu6Tr1FfPQ2/B3z/mjZrEPWmo96gs/eWdLxUC535bl1FsiQvqxVOQNbamzjvIi8sSNcpj/f205N1OEhq4t9BL13FZHQP7cTocnH1wknxvrCNCfG+rw6IvaV2HfXWXG6KkID+yp7jv3qVEEJGztU/ksWDpOP6bq+0ObqtvVqLL1OjEpq4XMRlBizn3Vx4OXmK4utQJ+pNZ6BfC7WtUq/JG66xXQb/arf6o2Y/RXfza0v3Z1U1ymTPRb8qMo8jq8liwJ7qmBxWRor/Wa7zQdtMeY8jgYOOWLN8iWhipSzEDZhnqr+v0VIaIhtKf+kmIf9/SaSejgqd803AvUR/EI0Po6zNzeHgGzW/6xeIxV/p1F978B&lt;/diagram&gt;&lt;diagram id=&quot;VQTaUeCvPUga8PmAW34j&quot; name=&quot;web_application&quot;&gt;7Zlvr5owFMY/TV9ukRYQX4KXe/diS5a4P7J3vVCBrVJSqqKffgWLQDDKbsyicBMTc56ettDfeQ4hADRf5y8cp9EXFhAK4CTIAXoCEGpwosm/QtkfFQspIeRxoJJqYREfiBInSt3EAclaiYIxKuK0LfosSYgvWhrmnO3aaStG27umOCQdYeFj2lV/xoGIlIosqx74ROIwUltbun4cWOMqWd1JFuGA7RoScgE0AUQ5BsgBhdb+oTlnTFxNq5LX+ZzQ4vCrcz3u+/z2BU73zkkibrUmz+Mfnvj6PfumHYyDE9q/vA8V7Ezsq3MngcSgQsZFxEKWYOrWqsPZJglIsazcyKlzPjOWSlGT4m8ixF7VFN4IJqVIrKkaJXkslsX0j4aKvMbIU65WLoN9FSSC7xuTitBrjtXTyqia1z1JdbgZ23CfXDobVfSYh0RcyFNlV5xbY4N+nGpaL4StibxwGXNCsYi3bR9gZafwlPdP5SBzVUXcuICOV7fFdKOuF7g6cCwws4FrAMsGtmZ3aqyuoAL6LooFWaS4pLGT3axdLauY0jmjjJdz0WpFTN+XeiY4+0MaI8F09jq5yHxLuCD5bShVy5jKQqrNaoaKd42mVXWiqNGvqnn3ClbrULvTzvB2h8OeDjfG7HDYw+HOkB2O0EAdDofvcNTT4eaYHY56OHw+ZIcbk4E6HF2g9hD+1Xv6dzpm/2rTsWCejRmzfqZNm8BygW2UbfoZ2GhUr1pwOpA2bTy6f433Nn0ds9HDv6N6kRqMf81H96/5/vy9jtns4d9RvSYNxb/TDlg7TWnsy8pkyXJ5U6SBQaxAP4fUgq/INP8z0tPXPoUUzWAXqX4GqWHdNdLZJaSeN2SkOnwUpGXulS+tar3GN27k/gU=&lt;/diagram&gt;&lt;diagram id=&quot;6qHfVrXy0bN7l5pNQlYw&quot; name=&quot;HexagonalArchitecture&quot;&gt;7ZhRr5owFMc/TR9NoAUtj1Txbg83WWKWLXvrpBeaWyipdeI+/VooKsJyt8V4vd4lJrZ/Dqd4fv/2GACaF/WDolX+KFMmAPTSGqAFgNCHnm++rLJvFYyckCmeuqCjsOI/mRM9p255yja9QC2l0Lzqi2tZlmytexpVSu76YU9S9FetaMYGwmpNxVD9wlOdOxVhfLzwgfEsd0vjIGgvFLQLdr9kk9NU7k4klAA4BRDVFCACrNb/oLmSUr8Y1gUX9ZwJW/yuru26y39PcPjtipX6QjlFCTd0wh4n/hapr98mE99/nsB2oR9UbF3ZXcn0vuNgqlfZYc5qmsnSrFoxxQummTqqnzrJFJfscq7ZqqJre9vOuNNouS6Emflm+MRr1vnNzjduaB+buOdhSrP6t7Xw/wTieTkemDRPqPZm7jKj0BnE7RDoh+18d/RbELiY/MRqqBOp83h2yP1XjEysw3RZqv6AKq2qJofNncqC8vLVOONrYw7gGWbsDTAjbwTz4Si8UcxouHmTGYgJiJcgCQFeghgNMJuC6z4nKnhmcC/WpvCWNbFYuDmIY3eh4Glqbx8FruS2TJl9zKtz9ftcERxynY7t3tumGoxQjYDxLG4HBuzC4o0iEOM7xhsGfbwwvAu84QDv5493TBGdb1LvLihORzbpFEQhIJ4dmB0aJ3aTkhhEyA7MNErs8YwTG3NJ4IqZzkq/N6ks3kryUjdlCwkIFzbXVsvNSSPWSj6zuRTSdvZSlqzp1kKcSdf0yQz1fQIDPPAJfns+mY36hBiH4MYnftOrp/Z4J8TawxiGLP/bY2APHPaPke5YObHH7O3ZA7+vZgDPWjoa+Sd+E82giX3hHYDLd/L2BSW/AA==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="162" y="10" width="660" height="90" rx="13.5" ry="13.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="192" y="25" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 55px; margin-left: 193px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-service-starter</div></div></div></foreignObject><text x="269" y="60" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-service-starter</text></switch></g><rect x="417" y="25" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 55px; margin-left: 418px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-job-starter</div></div></div></foreignObject><text x="494" y="60" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-job-starter</text></switch></g><rect x="642" y="25" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 55px; margin-left: 643px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-sync-starter</div></div></div></foreignObject><text x="719" y="60" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-sync-starter</text></switch></g><rect x="42" y="40" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 55px; margin-left: 43px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 23px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">starter</div></div></div></foreignObject><text x="87" y="62" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="23px" text-anchor="middle">starter</text></switch></g><rect x="162" y="130" width="660" height="90" rx="13.5" ry="13.5" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><rect x="192" y="145" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 175px; margin-left: 193px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-interface</div></div></div></foreignObject><text x="269" y="180" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-interface</text></switch></g><rect x="417" y="145" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 175px; margin-left: 418px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-interface-job</div></div></div></foreignObject><text x="494" y="180" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-interface-job</text></switch></g><rect x="642" y="145" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 175px; margin-left: 643px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-interface-sync</div></div></div></foreignObject><text x="719" y="180" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-interface-sync</text></switch></g><rect x="42" y="160" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 175px; margin-left: 43px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 23px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">interface</div></div></div></foreignObject><text x="87" y="182" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="23px" text-anchor="middle">interface</text></switch></g><rect x="162" y="250" width="660" height="90" rx="13.5" ry="13.5" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="192" y="265" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 295px; margin-left: 193px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-application</div></div></div></foreignObject><text x="269" y="300" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-application</text></switch></g><rect x="417" y="265" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 295px; margin-left: 418px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-application-job</div></div></div></foreignObject><text x="494" y="300" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-application-job</text></switch></g><rect x="642" y="265" width="153" height="60" rx="9" ry="9" fill="#eeeeee" stroke="#36393d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 295px; margin-left: 643px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-application-sync</div></div></div></foreignObject><text x="719" y="300" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-application-sy...</text></switch></g><rect x="42" y="280" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 295px; margin-left: 43px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 23px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">application</div></div></div></foreignObject><text x="87" y="302" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="23px" text-anchor="middle">applicat...</text></switch></g><rect x="162" y="370" width="660" height="90" rx="13.5" ry="13.5" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 658px; height: 1px; padding-top: 415px; margin-left: 163px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-domain</div></div></div></foreignObject><text x="492" y="420" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-domain</text></switch></g><rect x="42" y="400" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 415px; margin-left: 43px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 23px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">domain</div></div></div></foreignObject><text x="87" y="422" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="23px" text-anchor="middle">domain</text></switch></g><rect x="162" y="490" width="660" height="90" rx="13.5" ry="13.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 658px; height: 1px; padding-top: 535px; margin-left: 163px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">order-infrastructure</div></div></div></foreignObject><text x="492" y="540" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">order-infrastructure</text></switch></g><rect x="32" y="520" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 535px; margin-left: 33px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 23px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">infrastructure</div></div></div></foreignObject><text x="77" y="542" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="23px" text-anchor="middle">infrastr...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>