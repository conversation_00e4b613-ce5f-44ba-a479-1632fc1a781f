<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="463px" height="1180px" viewBox="-0.5 -0.5 463 1180"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="FU2sNviDecC8OtICUwQJ-16"><g><rect x="209" y="1" width="92" height="49" rx="7.35" ry="7.35" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 90px; height: 1px; padding-top: 26px; margin-left: 210px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">开始</div></div></div></foreignObject><text x="255" y="29" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">开始</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-17"><g><path d="M 219.25 136.25 L 248.43 107.07 Q 255.5 100 262.57 107.07 L 320.93 165.43 Q 328 172.5 320.93 179.57 L 262.57 237.93 Q 255.5 245 248.43 237.93 L 190.07 179.57 Q 183 172.5 190.07 165.43 Z" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 143px; height: 1px; padding-top: 173px; margin-left: 184px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">准备初始参数</div></div></div></foreignObject><text x="256" y="176" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">准备初始参数</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-18"><g><rect x="193" y="318" width="124" height="49" rx="7.35" ry="7.35" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 122px; height: 1px; padding-top: 343px; margin-left: 194px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">查询数据</div></div></div></foreignObject><text x="255" y="346" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">查询数据</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-19"><g><path d="M 286.75 458.75 L 321.43 424.07 Q 328.5 417 335.57 424.07 L 404.93 493.43 Q 412 500.5 404.93 507.57 L 335.57 576.93 Q 328.5 584 321.43 576.93 L 252.07 507.57 Q 245 500.5 252.07 493.43 Z" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 165px; height: 1px; padding-top: 501px; margin-left: 246px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">是否查询到数据?</div></div></div></foreignObject><text x="329" y="504" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">是否查询到数据?</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-20"><g><rect x="156" y="657" width="188" height="49" rx="7.35" ry="7.35" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 186px; height: 1px; padding-top: 682px; margin-left: 157px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">处理当前批次数据</div></div></div></foreignObject><text x="250" y="685" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">处理当前批次数据</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-21"><g><rect x="157" y="756" width="186" height="49" rx="7.35" ry="7.35" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 184px; height: 1px; padding-top: 781px; margin-left: 158px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">获取本批次最大ID</div></div></div></foreignObject><text x="250" y="784" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">获取本批次最大ID</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-22"><g><path d="M 204.75 900.75 L 243.43 862.07 Q 250.5 855 257.57 862.07 L 334.93 939.43 Q 342 946.5 334.93 953.57 L 257.57 1030.93 Q 250.5 1038 243.43 1030.93 L 166.07 953.57 Q 159 946.5 166.07 939.43 Z" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 181px; height: 1px; padding-top: 947px; margin-left: 160px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">是否还有更多数据?</div></div></div></foreignObject><text x="251" y="950" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">是否还有更多数据?</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-23"><g><rect x="0" y="1111" width="255" height="67" rx="10.05" ry="10.05" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 253px; height: 1px; padding-top: 1145px; margin-left: 1px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">使用最大ID作为下一页起始ID</div></div></div></foreignObject><text x="128" y="1148" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">使用最大ID作为下一页起始ID</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-24"><g><rect x="305" y="1120" width="156" height="49" rx="7.35" ry="7.35" fill="#f2cc8f" stroke="#e07a5f" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 154px; height: 1px; padding-top: 1145px; margin-left: 306px;"><div data-drawio-colors="color: #393C56; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(57, 60, 86); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">结束分页查询</div></div></div></foreignObject><text x="383" y="1148" fill="#393C56" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">结束分页查询</text></switch></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-25"><g><path d="M 255 50 Q 255 50 255.42 91.88" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 255.49 98.88 L 251.92 91.92 L 258.92 91.85 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-26"><g><path d="M 255.5 245 Q 255.5 245 255.06 309.88" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 255.01 316.88 L 251.56 309.86 L 258.56 309.91 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="11px"><text x="254.75" y="285.67">设置初始最大ID为0</text></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-27"><g><path d="M 290.96 367 Q 329 392 328.66 408.88" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 328.52 415.88 L 325.16 408.81 L 332.16 408.95 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-28"><g><path d="M 295.91 551.41 Q 250 620 250 648.88" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 250 655.88 L 246.5 648.88 L 253.5 648.88 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="11px"><text x="262.04" y="605.27">是</text></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-29"><g><path d="M 250 706 Q 250 706 250 747.88" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 250 754.88 L 246.5 747.88 L 253.5 747.88 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-30"><g><path d="M 250 805 Q 250 805 250.42 846.88" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 250.49 853.88 L 246.92 846.92 L 253.92 846.85 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-31"><g><path d="M 225.3 1012.8 Q 201 1074 169 1105.32" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 164 1110.22 L 166.55 1102.82 L 171.45 1107.82 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="11px"><text x="202.97" y="1071.79">是</text></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-32"><g><path d="M 119.85 1111 Q 112 1074 112 733 Q 112 392 185.33 366.71" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 191.94 364.42 L 186.47 370.02 L 184.18 363.4 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-33"><g><path d="M 359.15 553.35 Q 398 620 398 847 Q 398 1074 389.38 1112.08" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 387.84 1118.91 L 385.97 1111.31 L 392.79 1112.85 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="11px"><text x="397.5" y="836.02">否</text></g></g></g><g data-cell-id="FU2sNviDecC8OtICUwQJ-34"><g><path d="M 277.56 1010.94 Q 305 1074 349.47 1114.53" fill="none" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 354.64 1119.25 L 347.11 1117.12 L 351.83 1111.94 Z" fill="#e07a5f" stroke="#e07a5f" stroke-miterlimit="10" pointer-events="all"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="11px"><text x="304.32" y="1077.6">否</text></g></g></g></g></g></g></svg>