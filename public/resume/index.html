<!DOCTYPE html><html lang=gl><head><script>null===localStorage.getItem("theme")?document.documentElement.setAttribute("data-theme","lofi"):document.documentElement.setAttribute("data-theme",localStorage.getItem("theme"))</script><meta charset=utf-8><link href=/favicon.ico rel=icon type=image/png><meta content="width=device-width,initial-scale=1" name=viewport><meta content="Astro v4.5.14" name=generator><title>杨润康 Resume</title><style>@font-face{font-family:DM Sans Variable;font-style:normal;font-display:swap;font-weight:100 1000;src:url(/_astro/dm-sans-latin-ext-wght-normal.D1bw2c55.woff2) format("woff2-variations");unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans Variable;font-style:normal;font-display:swap;font-weight:100 1000;src:url(/_astro/dm-sans-latin-wght-normal.DeBecvsH.woff2) format("woff2-variations");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@media (max-width:768px){.flex-col[data-astro-cid-6uzmzu6t].justify-end{display:flex;flex-direction:column;justify-content:center;align-items:center}}body{font-family:DM Sans Variable,sans-serif}@media only screen and (max-width:480px){*{font-size:12px}}.overflow-y-scroll::-webkit-scrollbar{width:3px}.overflow-y-scroll::-webkit-scrollbar-thumb{background-color:oklch(var(--s))}.overflow-y-scroll{scrollbar-width:1px}@media (max-width:768px){.container{flex-direction:column}}@media (min-width:768px){.container{display:flex;flex-wrap:wrap}}</style><link href=/_astro/about.Bw1ftKvY.css rel=stylesheet><script type=module>var u={exports:{}};!function(t,e){function a(){var t=document.querySelector("[data-toggle-theme]"),e=t?t.getAttribute("data-key"):null;(function(a=localStorage.getItem(e||"theme")){localStorage.getItem(e||"theme")&&(document.documentElement.setAttribute("data-theme",a),t&&[...document.querySelectorAll("[data-toggle-theme]")].forEach((e=>{e.classList.add(t.getAttribute("data-act-class"))})))})(),t&&[...document.querySelectorAll("[data-toggle-theme]")].forEach((t=>{t.addEventListener("click",(function(){var a=t.getAttribute("data-toggle-theme");if(a){var c=a.split(",");document.documentElement.getAttribute("data-theme")==c[0]?1==c.length?(document.documentElement.removeAttribute("data-theme"),localStorage.removeItem(e||"theme")):(document.documentElement.setAttribute("data-theme",c[1]),localStorage.setItem(e||"theme",c[1])):(document.documentElement.setAttribute("data-theme",c[0]),localStorage.setItem(e||"theme",c[0]))}[...document.querySelectorAll("[data-toggle-theme]")].forEach((t=>{t.classList.toggle(this.getAttribute("data-act-class"))}))}))}))}function c(){var t=document.querySelector("[data-set-theme='']"),e=t?t.getAttribute("data-key"):null;(function(t=localStorage.getItem(e||"theme")){if(null!=t&&""!=t)if(localStorage.getItem(e||"theme")&&""!=localStorage.getItem(e||"theme")){document.documentElement.setAttribute("data-theme",t),(a=document.querySelector("[data-set-theme='"+t.toString()+"']"))&&([...document.querySelectorAll("[data-set-theme]")].forEach((t=>{t.classList.remove(t.getAttribute("data-act-class"))})),a.getAttribute("data-act-class")&&a.classList.add(a.getAttribute("data-act-class")))}else{var a;(a=document.querySelector("[data-set-theme='']")).getAttribute("data-act-class")&&a.classList.add(a.getAttribute("data-act-class"))}})(),[...document.querySelectorAll("[data-set-theme]")].forEach((t=>{t.addEventListener("click",(function(){document.documentElement.setAttribute("data-theme",this.getAttribute("data-set-theme")),localStorage.setItem(e||"theme",document.documentElement.getAttribute("data-theme")),[...document.querySelectorAll("[data-set-theme]")].forEach((t=>{t.classList.remove(t.getAttribute("data-act-class"))})),t.getAttribute("data-act-class")&&t.classList.add(t.getAttribute("data-act-class"))}))}))}function o(){var t=document.querySelector("select[data-choose-theme]"),e=t?t.getAttribute("data-key"):null;(function(t=localStorage.getItem(e||"theme")){localStorage.getItem(e||"theme")&&(document.documentElement.setAttribute("data-theme",t),document.querySelector("select[data-choose-theme] [value='"+t.toString()+"']")&&[...document.querySelectorAll("select[data-choose-theme] [value='"+t.toString()+"']")].forEach((t=>{t.selected=!0})))})(),t&&[...document.querySelectorAll("select[data-choose-theme]")].forEach((t=>{t.addEventListener("change",(function(){document.documentElement.setAttribute("data-theme",this.value),localStorage.setItem(e||"theme",document.documentElement.getAttribute("data-theme")),[...document.querySelectorAll("select[data-choose-theme] [value='"+localStorage.getItem(e||"theme")+"']")].forEach((t=>{t.selected=!0}))}))}))}u.exports={themeChange:function(t=!0){!0===t?document.addEventListener("DOMContentLoaded",(function(t){a(),o(),c()})):(a(),o(),c())}}}();var i=u.exports;i.themeChange()</script></head><body class="flex flex-col min-h-screen"><header class="relative mx-auto pb-2 pt-6" data-astro-cid-6uzmzu6t><article class="flex items-center flex-col gap-4 max-w-2xl md:flex-col w-full" data-astro-cid-6uzmzu6t><div class=text-center data-astro-cid-6uzmzu6t><div class="flex items-center justify-center mb-2 md:justify-between relative" data-astro-cid-6uzmzu6t><h1 class="font-extrabold tracking-tight md:text-3xl relative text-3xl" data-astro-cid-6uzmzu6t>杨润康</h1></div></div><div class="flex items-center justify-center mt-1" data-astro-cid-6uzmzu6t><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em data-astro-cid-6uzmzu6t><symbol id=ai:carbon:location><path d="M16 18a5 5 0 1 1 5-5a5.006 5.006 0 0 1-5 5m0-8a3 3 0 1 0 3 3a3.003 3.003 0 0 0-3-3" fill=currentColor /><path d="m16 30l-8.436-9.949a35.076 35.076 0 0 1-.348-.451A10.889 10.889 0 0 1 5 13a11 11 0 0 1 22 0a10.884 10.884 0 0 1-2.215 6.597l-.001.003s-.3.394-.345.447ZM8.813 18.395s.233.308.286.374L16 26.908l6.91-8.15c.044-.055.278-.365.279-.366A8.901 8.901 0 0 0 25 13a9 9 0 1 0-18 0a8.905 8.905 0 0 0 1.813 5.395" fill=currentColor /></symbol><use xlink:href=#ai:carbon:location></use></svg><p class="text-sm text-secondary mr-3" data-astro-cid-6uzmzu6t>四川成都</p><svg class="h-4 w-4 mr-1" data-icon=carbon:phone height=1em viewBox="0 0 32 32" width=1em data-astro-cid-6uzmzu6t><symbol id=ai:carbon:phone><path d="M26 29h-.17C6.18 27.87 3.39 11.29 3 6.23A3 3 0 0 1 5.76 3h5.51a2 2 0 0 1 1.86 1.26L14.65 8a2 2 0 0 1-.44 2.16l-2.13 2.15a9.37 9.37 0 0 0 7.58 7.6l2.17-2.15a2 2 0 0 1 2.17-.41l3.77 1.51A2 2 0 0 1 29 20.72V26a3 3 0 0 1-3 3M6 5a1 1 0 0 0-1 1v.08C5.46 12 8.41 26 25.94 27a1 1 0 0 0 1.06-.94v-5.34l-3.77-1.51l-2.87 2.85l-.48-.06c-8.7-1.09-9.88-9.79-9.88-9.88l-.06-.48l2.84-2.87L11.28 5Z" fill=currentColor /></symbol><use xlink:href=#ai:carbon:phone></use></svg><p class="text-sm text-secondary mr-3" data-astro-cid-6uzmzu6t>13955196045</p><svg class="h-4 w-4 mr-1" data-icon=carbon:email height=1em viewBox="0 0 32 32" width=1em data-astro-cid-6uzmzu6t><symbol id=ai:carbon:email><path d="M28 6H4a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2m-2.2 2L16 14.78L6.2 8ZM4 24V8.91l11.43 7.91a1 1 0 0 0 1.14 0L28 8.91V24Z" fill=currentColor /></symbol><use xlink:href=#ai:carbon:email></use></svg><p class="text-sm text-secondary mr-3" data-astro-cid-6uzmzu6t><EMAIL></p></div></article></header><div class="flex items-center absolute lg:top-8 md:top-6 top-4 xl:top-10 z-10 lg:right-8 md:right-6 right-4 xl:right-10"><svg class="h-4 w-4" data-icon=carbon:moon height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:moon><path d="M13.503 5.414a15.076 15.076 0 0 0 11.593 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3" fill=currentColor /></symbol><use xlink:href=#ai:carbon:moon></use></svg> <input checked=checked type=checkbox class="bg-secondary mx-1 toggle toggle-sm" data-act-class=ACTIVECLASS data-toggle-theme=black,lofi> <svg class="h-4 w-4" data-icon=carbon:light height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:light><path d="M15 2h2v5h-2zm6.688 6.9l3.506-3.506l1.414 1.414l-3.506 3.506zM25 15h5v2h-5zm-3.312 8.1l1.414-1.413l3.506 3.506l-1.414 1.414zM15 25h2v5h-2zm-9.606.192L8.9 21.686l1.414 1.414l-3.505 3.506zM2 15h5v2H2zm3.395-8.192l1.414-1.414L10.315 8.9L8.9 10.314zM16 12a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6" fill=currentColor /></symbol><use xlink:href=#ai:carbon:light></use></svg></div><div class="flex items-center absolute lg:top-8 md:top-6 top-4 xl:top-10 z-10 left-4 lg:left-8 md:left-6 xl:left-10"><a href=/ ><span class="link link-hover"><svg class="h-6 w-6" data-icon=carbon:undo height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:undo><path d="M20 10H7.815l3.587-3.586L10 5l-6 6l6 6l1.402-1.415L7.818 12H20a6 6 0 0 1 0 12h-8v2h8a8 8 0 0 0 0-16" fill=currentColor /></symbol><use xlink:href=#ai:carbon:undo></use></svg></span></a></div><main class="grid flex-grow max-h-[90vh] max-w-3xl md:max-h-[70vh] mx-auto py-2 w-[95vw]"><div class=container><div class="gap-1 join join-vertical"><html lang=gl><head><meta charset=UTF-8><meta content="width=device-width,initial-scale=1" name=viewport><title>CV Template</title></head><body><div class="collapse duration-700 ease-in-out"><input checked=checked type=checkbox aria-label=关于 disabled=disabled style=cursor:default><div class="font-extrabold md:text-2xl collapse-title tracking-tight"><div class=flex><svg class="h-8 mr-4 w-8" data-icon=carbon:identification height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:identification><path d="M28 6v20H4V6zm0-2H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h24a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2" fill=currentColor /><path d="M6 10h7v2H6zm0 4h4v2H6zm17 4h-6a3 3 0 0 0-3 3v2h2v-2a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2h2v-2a3 3 0 0 0-3-3m-3-1a4 4 0 1 0-4-4a4 4 0 0 0 4 4m0-6a2 2 0 1 1-2 2a2 2 0 0 1 2-2" fill=currentColor /></symbol><use xlink:href=#ai:carbon:identification></use></svg> 关于</div></div><div class=collapse-content><article class="prose text-justify"><ul><li>有大型电商系统和SOA服务化开发经验,熟悉高并发的业务场景</li></ul><ul><li>拥有高度抽象业务的能力,可基于模型和行为构建稳定、易扩展的业务系统</li></ul></article></div></div></body></html><html lang=gl><head><meta charset=UTF-8><meta content="width=device-width,initial-scale=1" name=viewport><title>CV Template</title></head><body><div class="collapse duration-700 ease-in-out"><input checked=checked type=checkbox aria-label=AI能力 disabled=disabled style=cursor:default><div class="font-extrabold md:text-2xl collapse-title tracking-tight"><div class=flex><svg class="h-8 mr-4 w-8" data-icon=carbon:ai height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:ai><path d="M17 11h3v10h-3v2h8v-2h-3V11h3V9h-8zm-4-2H9c-1.103 0-2 .897-2 2v12h2v-5h4v5h2V11c0-1.103-.897-2-2-2m-4 7v-5h4v5z" fill=currentColor /></symbol><use xlink:href=#ai:carbon:ai></use></svg> AI能力</div></div><div class=collapse-content><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><article class="text-secondary prose"><ul><li>主导 MCP 开发及提示词（Prompt）工程建设，推动 AI 能力在业务中的深度融合</li><li>基于 Dify 平台构建原生 AI 应用，探索多场景智能化解决方案，提升交互体验与业务处理效率</li></ul></article><div class="flex content-around flex-wrap gap-2 mt-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=ChatGPT>ChatGPT</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Claude>Claude</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=DeepSeek>DeepSeek</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Cursor>Cursor</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Dify>Dify</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=FastGPT>FastGPT</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key="Augment Code">Augment Code</span></div></div></div></div></div></div></div></body></html><html lang=gl><head><meta charset=UTF-8><meta content="width=device-width,initial-scale=1" name=viewport><title>CV Template</title></head><body><div class="collapse duration-700 ease-in-out"><input checked=checked type=checkbox aria-label=技术能力 disabled=disabled style=cursor:default><div class="font-extrabold md:text-2xl collapse-title tracking-tight"><div class=flex><svg class="h-8 mr-4 w-8" data-icon=carbon:skill-level height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:skill-level><path d="M30 30h-8V4h8zm-6-2h4V6h-4zm-4 2h-8V12h8zm-6-2h4V14h-4zm-4 2H2V18h8zm-6-2h4v-8H4z" fill=currentColor /></symbol><use xlink:href=#ai:carbon:skill-level></use></svg> 技术能力</div></div><div class=collapse-content><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid mb-2"><article class="text-secondary prose"><ul><li><strong>Java</strong>: 熟悉Java基础、常用集合框架、线程及异常处理等,拥有生产环境JVM排查经验</li><li><strong>数据库</strong>: 熟悉MySQL数据库及常用的优化方法,例如索引、分表、SQL调优等;使用过Oracle数据库</li><li><strong>框架</strong>: 熟悉Spring、Spring MVC、Spring Cloud、Spring Boot、MyBatis等主流框架</li><li><strong>消息队列</strong>: 熟悉Kafka和RabbitMQ,拥有丰富的系统异步化改造经验及重构经验</li><li><strong>缓存</strong>: 熟悉Redis, 热点数据缓存、分布式锁设计、有序自增Id、pub/sub异步写日志等</li><li><strong>RPC框架</strong>: 熟悉Dubbo,基于Dubbo进行服务化改造,提供中台服务能力</li><li><strong>分布式事务</strong>: 熟练使用分布式事务中间件Seata解决分布式事务问题</li><li><strong>配置中心</strong>: 熟练使用Apollo和Disconf分布式配置管理中心</li><li><strong>调度平台</strong>: 熟悉xxl-job分布式任务调度平台</li><li><strong>Linux</strong>: 熟练操作Linux操作系统.项目部署,日志检索,问题排查经验丰富</li><li><strong>Docker</strong>: 会使用Docker搭建运行时依赖环境以及部署和回滚应用</li><li><strong>开发工具</strong>: 熟练使用IDEA, Maven/Gradle,Git,docker,Sublime Text等开发工具</li><li><strong>Canal</strong>: 消费Canal数据,写入ES; 沉淀通用能力(ES操作、刷数、一致性校验);</li><li><strong>数据分析</strong>: 熟练使用阿里DataWorks和Metabase构建数据分析&#x26;决策系统</li></ul></article></div></div></div></div></div></body></html><html lang=gl><head><meta charset=UTF-8><meta content="width=device-width,initial-scale=1" name=viewport><title>CV Template</title></head><body><div class="collapse duration-700 ease-in-out"><input checked=checked type=checkbox aria-label=工作经历 disabled=disabled style=cursor:default><div class="font-extrabold md:text-2xl collapse-title tracking-tight"><div class=flex><svg class="h-8 mr-4 w-8" data-icon=carbon:construction height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:construction><path d="M29.34 16.06a1 1 0 0 0-1.108.3l-3.772 4.526l-5.435-.988l-3.602-8.952A3.014 3.014 0 0 0 12.612 9h-4.06a3.002 3.002 0 0 0-1.543.428L2 12.434v6.4l5 .91V30h2v-9.893l3.565.648L14 24.2V30h2v-6.2l-1.091-2.618l8.081 1.469l-4.758 5.709A1 1 0 0 0 19 30h10a1 1 0 0 0 1-1V17a1 1 0 0 0-.66-.94M4 17.166v-3.6l3-1.8v5.945zm5 .909V11h3.614a1.014 1.014 0 0 1 .945.67l3.14 7.805zM28 28h-6.865L28 19.762zM12.5 8A3.5 3.5 0 1 1 16 4.5A3.504 3.504 0 0 1 12.5 8m0-5A1.5 1.5 0 1 0 14 4.5A1.502 1.502 0 0 0 12.5 3" fill=currentColor /></symbol><use xlink:href=#ai:carbon:construction></use></svg> 工作经历</div></div><div class=collapse-content><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3 mb-2"><div class="col-span-2 justify-self-start self-start text-left"><h1 class="font-extrabold md:text-2xl card-title tracking-tighter uppercase">成都一心数科数字技术有限公司</h1><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:code height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:code><path d="m31 16l-7 7l-1.41-1.41L28.17 16l-5.58-5.59L24 9zM1 16l7-7l1.41 1.41L3.83 16l5.58 5.59L8 23zm11.42 9.484L17.64 6l1.932.517L14.352 26z" fill=currentColor /></symbol><use xlink:href=#ai:carbon:code></use></svg><p class="text-secondary font-light text-base">高级软件工程师</p><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-secondary font-light text-base">四川成都</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">2024.01-至今</div></div></div><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><div class="flex content-around flex-wrap gap-2 my-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Spring>Spring</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=SpringCloud>SpringCloud</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MySQL>MySQL</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=RocketMQ>RocketMQ</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Kafka>Kafka</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=RabbitMQ>RabbitMQ</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=ApolloConfig>ApolloConfig</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Doris>Doris</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=K8s>K8s</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=ES>ES</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Canal>Canal</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MongoDB>MongoDB</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=SkyWalking>SkyWalking</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Grafana>Grafana</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=ELK>ELK</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Arthas>Arthas</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=DDD>DDD</span></div><article class="text-secondary prose"><h4 id=技术架构与系统优化>技术架构与系统优化</h4><ol><li><strong>主导交易链路性能优化</strong>:完成交易全链路梳理，通过并行、异步处理、限流等手段，全面提升系统吞吐能力与稳定性</li><li><strong>构建千万级网关服务</strong>，覆盖系统压测、架构升级及性能调优，支撑高并发业务场景，保障系统稳定运行</li><li>优化SpringCloudGateway网关服务，解决直接内存溢出、资源高占用等生产问题，完成SCG大版本升级，增强系统可维护性与易用性</li><li><strong>自研服务优雅下线组件</strong>，兼容 Nacos、Kafka、RocketMQ、XXL-JOB 等主流中间件，保障服务发布过程无感知切换与流量平滑迁移</li><li><strong>推动基础组件升级</strong>（如日志组件、SkyWalking），提升系统可观测性与开发效率</li><li>基于<strong>DDD领域驱动设计</strong>重构订单 OMS 系统，提升系统模块化与业务扩展能力</li></ol><h4 id=业务支撑与数字化建设>业务支撑与数字化建设</h4><ol><li>主导“<strong>线下单系统</strong>”建设，稳定支撑日订单量 50W+，为线下门店数字化提供技术支撑</li><li>深度参与并推动一心堂数字化转型，构建业务中台，优化订单数据流转效率</li><li>技术方案沉淀与落地，持续推动团队技术能力建设</li></ol></article></div></div></div></div><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3 mb-2"><div class="col-span-2 justify-self-start self-start text-left"><h1 class="font-extrabold md:text-2xl card-title tracking-tighter uppercase">成都维沃尔科技有限公司</h1><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:code height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:code></use></svg><p class="text-secondary font-light text-base">高级软件工程师</p><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-secondary font-light text-base">四川成都</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">2022.03-2023.09</div></div></div><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><div class="flex content-around flex-wrap gap-2 my-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Spring>Spring</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Dubbo>Dubbo</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MySQL>MySQL</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=RabbitMQ>RabbitMQ</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Kafka>Kafka</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=ApolloConfig>ApolloConfig</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Presto>Presto</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Doris>Doris</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=K8s>K8s</span></div><article class="text-secondary prose"><ol><li><strong>Google搜索广告批量投放</strong>,日均广告系列维度投放量9000+,销售额提升2%,$10000/天; 人均人效提升8h/周;投放关键词覆盖整体普通搜索广告投放词量的65%</li><li><strong>Google Shopping广告投放管理</strong>,支撑公司业务部门日常高频操作;二期支持依据业务创建规则自动优化广告。业务操作渗透率达到50%;基于规则的自动化,在保证ROI的基础上,人均人效提升2d/周</li><li><strong>Google无效页面自动化处理</strong>,基于公司商品库存、自归因广告效果的指标数据,自动启用或暂停广告投放。系统日处理无效页面200+,节省Google广告预算$2000+/月,节省Google运营团队(20人)10h/天的运营工时</li><li><strong>网红KOL系统构建</strong>,入驻网红300+,网红渠道GMV达到300w</li><li><strong>短链技术服务构建</strong>,支撑公司网红系统和联盟客系统短链创建需求,月均短链创建数约4000条/月</li><li><strong>广告反作弊与创意中心系统方案输出与MVP开发</strong>, 结合公司广告业务现状,调研业界广告反作弊系统与创意管理平台,输出完整架构设计与技术方案,并深度参与反作弊系统与创意中心 MVP 功能落地与核心模块开发</li></ol></article></div></div></div></div><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3 mb-2"><div class="col-span-2 justify-self-start self-start text-left"><h1 class="font-extrabold md:text-2xl card-title tracking-tighter uppercase">成都斯达领科网络科技有限公司</h1><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:code height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:code></use></svg><p class="text-secondary font-light text-base">Java软件工程师</p><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-secondary font-light text-base">四川成都</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">2020.09-2022.01</div></div></div><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><div class="flex content-around flex-wrap gap-2 my-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key="Spring Cloud">Spring Cloud</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=分布式事务Seata>分布式事务Seata</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key="canal MySQL binlog增量订阅&#38;消费组件">canal MySQL binlog增量订阅&amp;消费组件</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MySQL>MySQL</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Kafka>Kafka</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=metabase数据分析&#38;决策>metabase数据分析&amp;决策</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=阿里DataWorks大数据开发治理平台>阿里DataWorks大数据开发治理平台</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Docker>Docker</span></div><article class="text-secondary prose"><ol><li><strong>构建统一订单模型</strong>：对接 Shopify、Amazon、Galaxy、SHOPLAZZA 等多个第三方平台订单,抽象标准订单模型,提升系统兼容性与扩展能力</li><li><strong>提供标准化对接接口</strong>：输出统一订单接口,支持自营站点接入 OMS 系统,降低系统耦合度,提升维护性与可扩展性</li><li><strong>订单通知系统解耦</strong>：基于 Canal 实时监听 MySQL binlog,实现订单消息异步通知与核心业务解耦,提升系统稳定性与触达成功率</li><li><strong>订单数据智能分析</strong>：利用 MaxCompute 引擎对订单数据进行分析优化,提升处理时效,降低客户投诉率</li><li><strong>构建订单履约可视化平台</strong>：基于 Metabase 搭建订单履约看板,支持数据驱动的业务决策与预警机制</li><li><strong>售后业务闭环重构</strong>:<ul><li>重构售后列表,提升售后查询效率、减少开发成本；</li><li>推动逆向订单一期/二期开发,完成售后全流程闭环,提升客服处理效率,减少订单流失</li></ul></li><li><strong>智能化客诉处理系统</strong>：开发客诉工单系统,支持实时客诉监控,显著提高处理响应速度</li><li><strong>开发订单运维工具</strong>：围绕抽象订单模型,开发多功能运维工具,提升系统自愈能力和故障容错能力</li><li><strong>订单告警系统建设</strong>：实现订单流程流转及关键字段（如价格、状态）实时监控与预警机制</li><li><strong>服务履约流程管控</strong>：支撑服务订单各环节监管,完善服务人员派单、履约、结单等流程,助力标准化运营落地</li></ol></article></div></div></div></div><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3 mb-2"><div class="col-span-2 justify-self-start self-start text-left"><h1 class="font-extrabold md:text-2xl card-title tracking-tighter uppercase">四川拓加电子商务股份有限公司</h1><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:code height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:code></use></svg><p class="text-secondary font-light text-base">Java软件工程师</p><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-secondary font-light text-base">四川成都</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">2019.09-2020.03</div></div></div><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><div class="flex content-around flex-wrap gap-2 my-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Hadoop>Hadoop</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Hive>Hive</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Sqoop>Sqoop</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Kettle>Kettle</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Redis>Redis</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=RabbitMQ>RabbitMQ</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MySQL>MySQL</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=SpringBoot>SpringBoot</span></div><article class="text-secondary prose"><ol><li><strong>推动研发流程标准化</strong>：主导搭建 Confluence 与 Jira 平台,规范研发流程与项目协作机制,提升团队知识沉淀与项目可追踪性</li><li><strong>构建企业级研发环境</strong>：搭建 Nexus 私服,推动代码仓库从 Gogs 迁移至 GitLab,标准化项目管理与权限控制,支撑多个研发团队协作</li><li><strong>营造技术氛围</strong>：主导开展多场技术分享与 Code Review,包括 Hadoop 数据仓库、RabbitMQ 消息队列、研发规范等,推动团队技术氛围提升</li><li><strong>数据平台建设（洞悉系统）</strong>：自主研发数据仓库管理平台“洞悉”,统一管理 Hadoop 状态、节点监控、任务监控、日志分析等功能,实现数据平台可视化、运维可控</li><li><strong>数据仓库业务研发框架开发</strong>：搭建数据仓库后台服务及业务开发框架,统一任务调度、异常捕获与流程执行,提升研发规范性和可维护性</li><li><strong>RabbitMQ中间件接入</strong>：在“烽火项目”中,设计并实现 RabbitMQ 消息流转平台,统一 MQ 消息进出通道,为系统异构对接提供支撑</li><li><strong>数据仓库分层规范建设</strong>：主导分层架构与数据清洗流程规范建设,辅助业务系统接入数据仓库,提升数仓整体治理与一致性</li><li><strong>CI/CD</strong>：结合 Jenkins与Nginx 实现内部系统蓝绿部署及一键发布,提升系统上线效率</li></ol></article></div></div></div></div><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3 mb-2"><div class="col-span-2 justify-self-start self-start text-left"><h1 class="font-extrabold md:text-2xl card-title tracking-tighter uppercase">深圳市环球易购电子商务有限公司</h1><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:code height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:code></use></svg><p class="text-secondary font-light text-base">中级软件工程师(11级)</p><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-secondary font-light text-base">广东深圳</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">2017.1-2019.7</div></div></div><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><div class="flex content-around flex-wrap gap-2 my-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Dubbo>Dubbo</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=RabbitMQ>RabbitMQ</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MySQL>MySQL</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Redis>Redis</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Elasticsearch>Elasticsearch</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=SpringBoot>SpringBoot</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Linux>Linux</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Scrum敏捷工作>Scrum敏捷工作</span></div><article class="text-secondary prose"><h4 id=订单中台soa-order>订单中台（SOA-ORDER）</h4><ol><li><strong>订单中台服务搭建</strong>,完成订单一期、二期、三期重构,逐步剥离非核心业务至异步系统,提升交易线稳定性</li><li><strong>推动订单工具系统搭建</strong>,构建异常处理流程体系,已承接购物车、营销、支付模块场景,节省每周3人天工时</li><li><strong>主导订单COD业务</strong>: COD（货到付款,支持菲律宾市场）与开放平台（商家入驻）,提升订单系统的业务承载能力</li><li><strong>建设开放API平台</strong>,赋能第三方商家对接,实现平台化扩展</li><li>订单服务多维度健康监控、平滑上线方案、上线流程标准化,降低发版失败率与流量损失</li><li>项目单元测试覆盖率达80%-95%,提前发现潜在问题,保障系统稳定</li><li>618、双11等大促期间技术支持响应,支撑 SOA-中台品牌形象</li></ol><h4 id=购物车中台soa-cart>购物车中台（SOA-CART）</h4><ol><li>购物车中台服务搭建,从单体应用到分布式系统</li><li>购物车一期重构,进行业务梳理,数据统一流入及流出,聚合分散接口,减少冗余字段,使用DTO进行业务流转</li><li>购物车二期重构,进行代码地毯式扫雷,架构组件进行服务化输出,业务日志规范,引入任务异步派发机制,降低业务间的耦合度</li><li>购物车三期重构,进行购物车业务区分,业务前置校验,缓存结构重新设计,职责明确,提供基础数据服务</li></ol><!-- #### 团队建设与研发管理

1. 推动研发规范落地,制定打卡机制、代码规范、Git 分支管理、数据变更流程
2. 主导CodeReview责任制度,强化代码质量管控
3. 担任订单团队 owner,统筹资源推动项目交付；同时担任导师培训新人,完善成长路径；作为讲师输出技术分享《订单系统从混沌到精细化演进》
4. 建立团队节奏机制：每周三次站会、季度复盘会、每日静默期,打造高效透明团队文化
5. 实施Scrum敏捷工作模式,小团队高效率,高质量完成需求 --><!-- 
### 工作内容
1. 业务支持,常规需求开发及线上系统维护,为订单服务稳定性提供保障
2. 订单业务梳理,输出优化文档及重构实施方案
3. 订单重构,非核心业务异步化,建立门面系统,提供组件化服务
4. 订单日常工作规范制定,线上打卡,代码规范,gitlab分支管理,数据变更流程输出
5. 担任owner角色,推动项目达成,协调资源,打配合战,促使项目顺利完成
6. 定期推动订单项目CodeReview,责任到人,为订单系统稳定性提供坚实的基础保障
7. 主导订单COD(货到付款)和开放平台(商家入驻)项目,为订单、SOA团队、公司赋能
8. 架构组件接入,构建多维度数据监控,订单服务健康监控
9. 订单平滑上线方案输出及上线流程制定,减少流量损失及发版失败率
10. 订单工具服务搭建,可以承接多模块及多站,节省每周3人天
11. 担任导师,培训新员工,完善新员工入职成长体系建设
12. 担任讲师,面向技术中心,分享订单从混沌服务到体系精细化演进讲解
13. 618,双11等大促时间段提供技术支持,积极响应线上问题,打出SOA-中台品牌
14. 推动项目单元测试覆盖率,发现潜在问题,内部解决. SOA订单内部项目单元测试覆盖率高至80%-95%

 #### 中台-SOA-订单
1. 订单中台服务搭建,从单体应用到分布式系统
2. 订单一期重构,剥离非核心业务至异步系统,提升交易线服务的稳定性	
3. 订单工具系统搭建,建立订单异常处理流程体系. 打造SOA内部工具产品,承接其他模块异常处理场景。目前已经承接了购物车、营销、支付模块
4. 订单二期重构,主要目标是职责分明,按照业务场景进行拆分
5. 订单三期重构,进行订单完成支付和订单取消的业务梳理及垂直服务建立
6. 订单新业务类型承接,COD(货到付款,针对菲律宾市场)及开放平台(商家入驻)
7. 开放API平台搭建,赋能商家

#### 中台-SOA-购物车
1. 购物车中台服务搭建,从单体应用到分布式系统
2. 购物车一期重构,进行业务梳理,数据统一流入及流出,聚合分散接口,减少冗余字段,使用DTO进行业务流转
3. 购物车二期重构,进行代码地毯式扫雷,架构组件进行服务化输出,业务日志规范,引入任务异步派发机制,降低业务间的耦合度
4. 购物车三期重构,进行购物车业务区分,业务前置校验,缓存结构重新设计,职责明确,提供基础数据服务

### 团队方面
1. 每周一、三、五站会,工作内容及计划汇报,线上问题点抛出,团队成员透明
2. 每季度回顾会,主题是分享,感悟,谈人生及理想
3. 每天团队有固定静默期,静默管理员负责统一处理问题,其余组员专心工作
4. Scrum团队工作模式,精悍战队,快速承接需求,高质量输出,专业化提供服务 
 --></article></div></div></div></div><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3 mb-2"><div class="col-span-2 justify-self-start self-start text-left"><h1 class="font-extrabold md:text-2xl card-title tracking-tighter uppercase">安徽慧通互联科技有限公司</h1><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:code height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:code></use></svg><p class="text-secondary font-light text-base">中级软件工程师</p><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-secondary font-light text-base">安徽合肥</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">2015.10-2016.10</div></div></div><div class="gap-4 grid place-items-center"><div class="col-span-2 justify-self-start self-start text-left"><div class="flex content-around flex-wrap gap-2 my-4"><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=SpringMVC>SpringMVC</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MyBatis>MyBatis</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Linux>Linux</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Tomcat>Tomcat</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=RabbitMQ>RabbitMQ</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=MySQL>MySQL</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Oracle>Oracle</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Redis>Redis</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=Axis2>Axis2</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=FreeMarker>FreeMarker</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=HighCharts>HighCharts</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=EasyUI>EasyUI</span><span class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max mb-1 text-sm" :key=XXL-JOB>XXL-JOB</span></div><article class="text-secondary prose"><!-- ### 工作内容 --><ol><li><strong>从0到1构建货车ETC账务系统</strong>。主导账务核心模块(黑名单、消费、充值还款)研发。</li><li>系统架构优化。引入RabbitMQ对账务系统进行解耦,异步处理非实时业务,系统TPS提升15.7%,系统高耦合度降低50%以上,显著提升系统稳定性与可维护性。</li><li><strong>从0到1构建数据看板</strong>。支撑10+业务数据指标,助力公司内部产品线制定策略</li><li><strong>从0到1构建短信平台</strong>。实现短信服务商高可用机制,系统短信处理成功率提升至99.9%</li><li><strong>统一任务调度框架</strong>。主导XXL-JOB框架的调研与落地。助力业务系统批处理业务稳定运行</li></ol><!-- #### 一、账务系统
   1.核心业务研发,例如黑名单,消费数据处理,充值还款模块
   2.增值业务研发,例如短信提醒,丰富公司业务维度
   3.产品套餐管理页面研发,为套餐管理提供统一数据入口
   4.产品运营数据可视化,提供基础业务预警功能,例如,开卡量锐减预警
   5.线上Oracle生产数据库维护及存储过程研发,关注数据增量及优化方案预研
   6.账务系统解耦,针对非及时性业务,使用MQ进行业务剥离及与外部系统解耦

#### 二、大屏系统

##### 背景
公司领导需要查看产品运营情况,需要一个独立的数据可视化系统,为产品规划及领导决策支持提供数据支撑.
##### 工作内容
1. 独立完成大屏系统研发工作,协调测试资源,运维资源,推动项目顺利上线运行
2. 大屏系统可行性评估及研发方案设计,研发方案设计生命周期为半年
3. 大屏系统扩展性、可控性评估工作,输出详细文档及会议组织讨论
4. 使用DBLink跨库操作数据及存储过程编写及优化方案输出
5. 后端任务调度程序研发,调度任务可控,降低高峰期时任务调度频率


#### 三、定时任务框架调整

##### 背景
原始的定时任务具体执行时间写在配置文件当中,做不到对单个定时任务的可控,比如说暂停,运行,停止等操作,没有一个统一地方进行管控和管理

##### 工作内容
1. 独立完成定时任务框架调整,接入开源框架xxl-job
2. 经验分享,提供接入xxl-job技术支持。成果:网厅系统成功接入xxl-job任务调度框架
3. 新框架开发文档输出,方便团队成员编码工作,专注业务开发,提供高质量服务
4. 持续在GitHub中跟进xxl-job版本

#### 四、短信平台

##### 背景
在产品的短信发送过程中会有发送失败的情况,短信服务提供商不稳定,导致系统短信业务出现问题,增加运营压力及难度

##### 工作内容
1. 负责短信服务提供商接入工作,多服务商提供,对外提供统一接口
2. 提供http和webservice两种服务调用方式,满足业务需求
3. Redis服务的安装以及使用,缓存热点数据
4. 多短信服务商管理页面研发,提供统一入口,进行数据管理 --></article></div></div></div></div></div></div></body></html><html lang=gl><head><meta charset=UTF-8><meta content="width=device-width,initial-scale=1" name=viewport><title>CV Template</title></head><body><div class="collapse duration-700 ease-in-out"><input checked=checked type=checkbox aria-label=学历 disabled=disabled style=cursor:default><div class="font-extrabold md:text-2xl collapse-title tracking-tight"><div class=flex><svg class="h-8 mr-4 w-8" data-icon=carbon:education height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:education><path d="M26 30h-2v-3a5.006 5.006 0 0 0-5-5h-6a5.006 5.006 0 0 0-5 5v3H6v-3a7.008 7.008 0 0 1 7-7h6a7.008 7.008 0 0 1 7 7zM5 6a1 1 0 0 0-1 1v9h2V7a1 1 0 0 0-1-1" fill=currentColor /><path d="M4 2v2h5v7a7 7 0 0 0 14 0V4h5V2Zm7 2h10v3H11Zm5 12a5 5 0 0 1-5-5V9h10v2a5 5 0 0 1-5 5" fill=currentColor /></symbol><use xlink:href=#ai:carbon:education></use></svg> 学历</div></div><div class=collapse-content><div class="my-4 border border-[oklch(var(--s))] card card-bordered card-compact hover:shadow-lg lg:w-128 md:w-112 transition-all w-full"><div class=card-body><div class="gap-4 grid grid-cols-3"><div class="col-span-2 justify-self-start self-start text-left"><div class="tracking-tighter uppercase md:text-xl">安徽农业大学</div><div class="flex items-center mt-2"><svg class="h-4 w-4 mr-1" data-icon=carbon:location height=1em viewBox="0 0 32 32" width=1em><use xlink:href=#ai:carbon:location></use></svg><p class="text-sm text-secondary font-light">安徽合肥</p></div></div><div class="justify-self-end self-center"><div class="text-center align-middle badge badge-outline h-max inline-block shadow-md w-max font-mono">电子信息工程</div></div></div></div></div></div></div></body></html><html lang=gl><head><meta charset=UTF-8><meta content="width=device-width,initial-scale=1" name=viewport><title>CV Template</title></head><body><div class="collapse duration-700 ease-in-out"><input checked=checked type=checkbox aria-label="Side Project" disabled=disabled style=cursor:default><div class="font-extrabold md:text-2xl collapse-title tracking-tight"><div class=flex><svg class="h-8 mr-4 w-8" data-icon=carbon:smoothing height=1em viewBox="0 0 32 32" width=1em><symbol id=ai:carbon:smoothing><defs/><circle cx=16 cy=16 fill=currentColor r=5 /><path d="M7.7 4.7a14.703 14.703 0 0 0-3 3.1L6.3 9a13.263 13.263 0 0 1 2.6-2.7z" fill=currentColor /><path d="M4.6 12.3l-1.9-.6A12.511 12.511 0 0 0 2 16h2a11.476 11.476 0 0 1 .6-3.7z" fill=currentColor /><path d="M2.7 20.4a14.403 14.403 0 0 0 2 3.9l1.6-1.2a12.887 12.887 0 0 1-1.7-3.3z" fill=currentColor /><path d="M7.8 27.3a14.403 14.403 0 0 0 3.9 2l.6-1.9A12.887 12.887 0 0 1 9 25.7z" fill=currentColor /><path d="M11.7 2.7l.6 1.9A11.476 11.476 0 0 1 16 4V2a12.511 12.511 0 0 0-4.3.7z" fill=currentColor /><path d="M24.2 27.3a15.18 15.18 0 0 0 3.1-3.1L25.7 23a11.526 11.526 0 0 1-2.7 2.7z" fill=currentColor /><path d="M27.4 19.7l1.9.6A15.475 15.475 0 0 0 30 16h-2a11.476 11.476 0 0 1-.6 3.7z" fill=currentColor /><path d="M29.2 11.6a14.403 14.403 0 0 0-2-3.9l-1.6 1.2a12.887 12.887 0 0 1 1.7 3.3z" fill=currentColor /><path d="M24.1 4.6a14.403 14.403 0 0 0-3.9-2l-.6 1.9a12.887 12.887 0 0 1 3.3 1.7z" fill=currentColor /><path d="M20.3 29.3l-.6-1.9a11.476 11.476 0 0 1-3.7.6v2a21.42 21.42 0 0 0 4.3-.7z" fill=currentColor /></symbol><use xlink:href=#ai:carbon:smoothing></use></svg> Side Project</div></div><div class=collapse-content><article class="prose text-justify"><ul><li><a href=https://www.moatkon.com>Moatkon网站</a></li><li><a href=https://github.com/yangrunkang/upupor>UGC网站Upupor</a></li><li>电台《程序员的生活记录》网易云电台,荔枝FM,喜马拉雅可播放</li></ul></article></div></div></body></html></div></div></main></body></html>